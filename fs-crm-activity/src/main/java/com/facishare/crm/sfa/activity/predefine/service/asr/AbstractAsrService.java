package com.facishare.crm.sfa.activity.predefine.service.asr;

import com.facishare.crm.sfa.activity.predefine.service.ActivityGeneralService;
import com.facishare.crm.sfa.activity.predefine.service.ActivityUserService;
import com.facishare.crm.sfa.activity.predefine.service.InteractiveDocumentService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ASR服务抽象基类
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 提供ASR服务的通用功能实现
 */
@Slf4j
public abstract class AbstractAsrService implements AsrService {

    @Resource
    protected ActivityMongoDao activityMongoDao;

    @Resource
    protected ActivityGeneralService activityGeneralService;

    @Resource
    protected NomonProducer nomonProducer;

    @Resource
    protected GDSHandler gdsHandler;

    @Resource
    protected IObjectDataService objectDataService;

    @Resource
    protected ActivityRocketProducer activityRocketProducer;

    @Autowired
    protected ActivityUserService activityUserService;

    @Autowired
    protected InteractiveDocumentService interactiveDocumentService;

    @Override
    public ActivityText.Result saveMeetingDocResult(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult) {
        try {
            String language = context.getLang().getValue();
            if ("fs-crm-meeting".equals(context.getPeerName())) {
                language = getLanguage(context);
            }
            
            // 纠错处理
            correct(context, meetingDocResult);
            
            // 转换为交互文档
            List<InteractiveDocument> interactiveDocuments = convertToInteractiveDocument(context.getTenantId(), meetingDocResult);
            
            // 更新活动用户信息
            activityUserService.upsertActivityUser(context, meetingDocResult, interactiveDocuments);
            
            // 批量插入文档
            activityMongoDao.batchInsert(context.getTenantId(), interactiveDocuments);
            
            // 更新活动文本内容
            String existContent = getExistContent(context, meetingDocResult.getObjectId());
            StringBuilder sb = new StringBuilder(existContent + "\n");
            interactiveDocuments.forEach(document -> {
                sb.append(document.getContent());
            });
            updateActivityText(context.getTenantId(), meetingDocResult.getObjectId(), meetingDocResult.getObjectApiName(), sb.toString());
            
            // 发送MQ消息
            boolean shouldSendMq = meetingDocResult.getContent().stream()
                    .anyMatch(content -> content.getId() % 5 == 0);
            if (shouldSendMq) {
                sendMQ(meetingDocResult.getObjectId(), context.getTenantId(), context.getUser().getUserId(), 
                       "realtime2text", "realtime2text", language);
            }
            
            return ActivityText.Result.builder()
                    .code(0)
                    .msg("success")
                    .success(true)
                    .build();
                    
        } catch (Exception e) {
            log.error("Save meeting doc result failed", e);
            return ActivityText.Result.builder()
                    .code(-1)
                    .msg("Failed to save meeting doc result: " + e.getMessage())
                    .success(false)
                    .build();
        }
    }

    @Override
    public boolean isServiceAvailable(ServiceContext context) {
        try {
            // 检查必要的配置和依赖是否可用
            return checkServiceDependencies(context);
        } catch (Exception e) {
            log.error("Check service availability failed for provider: {}", getProviderName(), e);
            return false;
        }
    }

    /**
     * 检查服务依赖是否可用
     */
    protected abstract boolean checkServiceDependencies(ServiceContext context);

    /**
     * 获取语言设置
     */
    protected String getLanguage(ServiceContext context) {
        // 默认实现，子类可以重写
        return context.getLang().getValue();
    }

    /**
     * 纠错处理
     */
    protected void correct(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult) {
        // 默认空实现，子类可以重写
    }

    /**
     * 转换为交互文档
     */
    protected List<InteractiveDocument> convertToInteractiveDocument(String tenantId, ActivityText.SaveMeetingDocResult meetingDocResult) {
        // 这里需要实现具体的转换逻辑
        // 由于涉及复杂的业务逻辑，建议从原有代码中提取
        throw new UnsupportedOperationException("convertToInteractiveDocument method should be implemented by subclass");
    }

    /**
     * 获取已存在的内容
     */
    protected String getExistContent(ServiceContext context, String objectId) {
        // 这里需要实现具体的获取逻辑
        // 由于涉及复杂的业务逻辑，建议从原有代码中提取
        return "";
    }

    /**
     * 更新活动文本
     */
    protected void updateActivityText(String tenantId, String objectId, String objectApiName, String fullText) {
        // 这里需要实现具体的更新逻辑
        // 由于涉及复杂的业务逻辑，建议从原有代码中提取
    }

    /**
     * 发送MQ消息
     */
    protected void sendMQ(String objectId, String tenantId, String userId, String actionCode, String stage, String language) {
        // 这里需要实现具体的MQ发送逻辑
        // 由于涉及复杂的业务逻辑，建议从原有代码中提取
    }

    /**
     * 生成发言人唯一标识
     */
    protected String generateSpeakerKey(String userName, String userId, String userApiName) {
        return String.format("%s_%s_%s", userName, userId, userApiName);
    }

    /**
     * 构建OSS路径
     */
    protected String buildOssPath(ServiceContext context, Object credentialData) {
        // 默认实现，子类可以重写
        return "";
    }
}
