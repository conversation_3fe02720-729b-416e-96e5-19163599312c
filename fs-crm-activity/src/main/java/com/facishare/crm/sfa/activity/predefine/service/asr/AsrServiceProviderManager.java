package com.facishare.crm.sfa.activity.predefine.service.asr;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * ASR服务提供者管理器
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 管理不同的ASR服务提供商，支持动态选择和路由
 */
@Component
@Slf4j
public class AsrServiceProviderManager implements ApplicationContextAware {
    
    private static final Map<String, AsrService> serviceMap = Maps.newHashMap();
    
    // 默认ASR服务提供商
    private static final String DEFAULT_PROVIDER = "aliyun";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initAsrServiceMap(applicationContext);
    }

    /**
     * 初始化ASR服务映射
     */
    private void initAsrServiceMap(ApplicationContext applicationContext) {
        Map<String, AsrService> springBeanMap = applicationContext.getBeansOfType(AsrService.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getProviderName())) {
                serviceMap.put(provider.getProviderName(), provider);
                log.info("Registered ASR service provider: {}", provider.getProviderName());
            }
        });
        log.info("Total ASR service providers registered: {}", serviceMap.size());
    }

    /**
     * 根据提供商名称获取ASR服务
     * 
     * @param providerName 提供商名称
     * @return ASR服务实例
     */
    public AsrService getService(String providerName) {
        if (StringUtils.isEmpty(providerName)) {
            providerName = DEFAULT_PROVIDER;
        }
        
        AsrService service = serviceMap.get(providerName);
        if (service == null) {
            log.warn("ASR service provider '{}' not found, using default provider '{}'", 
                    providerName, DEFAULT_PROVIDER);
            service = serviceMap.get(DEFAULT_PROVIDER);
        }
        
        if (service == null) {
            throw new IllegalStateException("No ASR service provider available");
        }
        
        return service;
    }

    /**
     * 获取默认ASR服务
     * 
     * @return 默认ASR服务实例
     */
    public AsrService getDefaultService() {
        return getService(DEFAULT_PROVIDER);
    }

    /**
     * 检查提供商是否可用
     * 
     * @param providerName 提供商名称
     * @return 是否可用
     */
    public boolean isProviderAvailable(String providerName) {
        return serviceMap.containsKey(providerName);
    }

    /**
     * 获取所有可用的提供商名称
     * 
     * @return 提供商名称集合
     */
    public java.util.Set<String> getAvailableProviders() {
        return serviceMap.keySet();
    }

    /**
     * 根据业务规则选择最佳的ASR服务提供商
     * 
     * @param tenantId 租户ID
     * @param language 语言类型
     * @return 推荐的提供商名称
     */
    public String selectBestProvider(String tenantId) {
        return isProviderAvailable("tencent") ? "tencent" : DEFAULT_PROVIDER;
    }
}
