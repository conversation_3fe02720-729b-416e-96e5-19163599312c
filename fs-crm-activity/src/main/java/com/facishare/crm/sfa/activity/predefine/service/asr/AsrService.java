package com.facishare.crm.sfa.activity.predefine.service.asr;

import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;

import java.util.Map;

/**
 * ASR服务策略接口
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 统一的ASR服务接口，支持不同的ASR提供商实现
 */
public interface AsrService {

    /**
     * 获取ASR服务提供商名称
     * 
     * @return 提供商名称，如 "aliyun", "tencent"
     */
    String getProviderName();

    /**
     * 开始实时转写任务
     * 
     * @param context 服务上下文
     * @param arg 实时转写开始参数
     * @return 任务结果，包含taskId和wsUrl等信息
     */
    TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg);

    /**
     * 停止实时转写任务
     * 
     * @param context 服务上下文
     * @param arg 实时转写停止参数
     * @return 停止结果
     */
    Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg);

    /**
     * 保存会议转写结果
     * 
     * @param context 服务上下文
     * @param meetingDocResult 会议文档结果
     * @return 保存结果
     */
    ActivityText.Result saveMeetingDocResult(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult);

    /**
     * 检查服务是否可用
     * 
     * @param context 服务上下文
     * @return 是否可用
     */
    boolean isServiceAvailable(ServiceContext context);

    /**
     * 获取服务配置信息
     * 
     * @param context 服务上下文
     * @return 配置信息
     */
    Map<String, Object> getServiceConfig(ServiceContext context);

    /**
     * 任务结果类
     */
    class TaskResult {
        private String wsUrl;
        private String taskId;
        private String provider;
        private Map<String, Object> extra;

        public TaskResult() {}

        public TaskResult(String wsUrl, String taskId, String provider) {
            this.wsUrl = wsUrl;
            this.taskId = taskId;
            this.provider = provider;
        }

        // Getters and Setters
        public String getWsUrl() {
            return wsUrl;
        }

        public void setWsUrl(String wsUrl) {
            this.wsUrl = wsUrl;
        }

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public Map<String, Object> getExtra() {
            return extra;
        }

        public void setExtra(Map<String, Object> extra) {
            this.extra = extra;
        }
    }
}
