<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-sfa</artifactId>
    <version>9.7.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>fs-crm-sales</module>
        <module>fs-crm-web</module>
        <module>fs-crm-payment</module>
        <module>fs-crm-common</module>
        <module>fs-crm-prm</module>
        <module>fs-crm-paas-ext</module>
        <module>fs-crm-management</module>
        <module>fs-crm-project-manage</module>
        <module>fs-domain-sfa</module>
        <module>fs-crm-master-data</module>
        <module>fs-crm-provider-sfa</module>
        <module>fs-crm-loyalty</module>
        <module>fs-crm-activity</module>
        <module>fs-crm-profile</module>
        <module>fs-crm-sfa-test1</module>
        <module>fs-crm-sfa-test2</module>
        <module>fs-crm-sfa-test3</module>
        <module>fs-crm-sfa-test4</module>
        <module>fs-crm-sfa-test5</module>
        <module>fs-crm-sfa-test6</module>
        <module>fs-crm-sfa-test7</module>
        <module>fs-crm-sfa-test8</module>
        <module>fs-crm-sfa-test9</module>
        <module>fs-crm-sfa-test10</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <jdk.version>1.8</jdk.version>
        <war.name>fs</war.name>
        <appframework.version>9.6.5-SNAPSHOT</appframework.version>
        <i18n-util.version>1.4-SNAPSHOT</i18n-util.version>
        <okio.version>3.2.0</okio.version>
        <servicelib-fsi.version>1.4-SNAPSHOT</servicelib-fsi.version>
        <erpdss.version>1.1-SNAPSHOT</erpdss.version>
        <mockito.version>3.3.0</mockito.version>
        <webApp.contextPath/>
    </properties>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>logconfig-core</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>

            <!--租户级配置-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-bizconf-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>rocketmq-spring-support</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-app-center-common-utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-union-core-api</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-proxy-core-api</artifactId>
                <version>0.0.8-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.thoughtworks.xstream</groupId>
                        <artifactId>xstream</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 文件系统后台接口代理 -->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-fsi-proxy</artifactId>
                <version>${fs-fsi-proxy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-sandbox-api</artifactId>
                <version>2.0.4-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-paas-gnomon-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>i18n-client</artifactId>
                <version>${i18n-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>${i18n-util.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.11</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-api</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-rocketmq-support</artifactId>
                <version>${rocketmq-support.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${apache.rocketmq.version}</version>
            </dependency>

            <!-- 指定 okio 的版本 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.0.Final</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.10.15</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>1.10.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>JaCoCo Agent</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>merge-coverage-data</id>
                        <phase>test</phase>
                        <goals>
                            <goal>merge</goal>
                        </goals>
                        <configuration>
                            <destFile>../fs-crm-sales/target/jacoco.exec</destFile>
                            <fileSets>
                                <fileSet>
                                    <directory>../</directory>
                                    <includes>
                                        <include>**/fs-crm-sfa-test*/target/*.exec</include>
                                    </includes>
                                </fileSet>
                            </fileSets>
                        </configuration>
                    </execution>
                    <execution>
                        <id>JaCoCo Report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>
                        @{argLine}
                    </argLine>
                    <forkCount>5</forkCount>
                    <parallel>none</parallel>
                    <!--                    <parallel>classes</parallel>-->
                    <!--                    <threadCount>5</threadCount>-->
                    <!--                    <forkCount>5</forkCount>-->
                    <!-- 其他配置 -->
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>fs</id>
            <name>fs-snapshot</name>
            <url>https://maven.foneshare.cn/artifactory/libs-snapshot-local</url>
        </snapshotRepository>
        <repository>
            <id>fs</id>
            <name>fs-release</name>
            <url>https://maven.foneshare.cn/artifactory/libs-release-local</url>
        </repository>
    </distributionManagement>
</project>
