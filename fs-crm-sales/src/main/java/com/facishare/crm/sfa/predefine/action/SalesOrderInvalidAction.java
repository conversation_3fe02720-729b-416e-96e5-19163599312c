package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.action.listener.SalesOrderInvalidActionListener;
import com.facishare.crm.sfa.predefine.service.BizCommonConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidAfterModel;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/8/7 16:53
 */
@Slf4j
public class SalesOrderInvalidAction extends StandardInvalidAction {
    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);
    private static final BizCommonConfigService bizCommonConfigService = SpringUtil.getContext().getBean(BizCommonConfigService.class);

    private Map<String, ApprovalFlowStartResult> maps = Maps.newHashMap();

    private String lifeStatus = "";
    private boolean newInvoice = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        // 做开票2.0的判断
        newInvoice = bizCommonConfigService.isOpen(IModuleInitService.MODULE_NEW_INVOICE, actionContext.getUser());
        if (newInvoice) {
            dataList.forEach(objectData -> {
                findNewInvoiceLineByOrderId(objectData.getId());

                String invoiceStatus = objectData.get("invoice_status", String.class);
                if (!Objects.equals("3", invoiceStatus)) {
                    throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
                }

                BigDecimal invoicedAmount = objectData.get("invoice_amount") == null ? BigDecimal.ZERO
                    : objectData.get("invoice_amount", BigDecimal.class);
                if (invoicedAmount.compareTo(BigDecimal.ZERO) > 0) {
                    throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
                }
            });
        }
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
            Lists.newArrayList(arg.getObjectDataId()), actionContext.getObjectApiName());
        if (CollectionUtils.notEmpty(objectDataList)) {
            lifeStatus = objectDataList.get(0).get("life_status", String.class);
        }
    }

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(SalesOrderInvalidActionListener.class);
        return classList;
    }

    @Override
    protected Map<String, ApprovalFlowStartResult> tryToStartInvalidApprovalFlow(List<IObjectData> objectDataListToTriggerApprovalFlow) {
        maps = super.tryToStartInvalidApprovalFlow(objectDataListToTriggerApprovalFlow);
        return maps;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        if (result.getObjectData() != null) {
            ObjectDataDocument documentData = result.getObjectData();
            IObjectData data = ObjectDataExt.of(documentData);
            if (CollectionUtils.notEmpty(maps)) {
                String id = data.getId();
                ApprovalFlowStartResult approvalFlowStartResult = maps.get(id);
                if (approvalFlowStartResult == ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
                    invalidAfter(data);
                }
            } else {
                invalidAfter(data);
            }
        }
        return result;
    }

    private void invalidAfter(IObjectData data) {
        SalesOrderInvalidAfterModel.Arg serviceArg = new SalesOrderInvalidAfterModel.Arg();
        serviceArg.setDataId(data.getId());
        serviceArg.setBeforeLifeStatus(lifeStatus);
        serviceArg.setAfterLifeStatus(data.get("life_status", String.class));
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
            "InvalidAfter");
        SalesOrderInterceptorModel.InvalidAfterResult invalidAfterResult = salesOrderBizProxy.invalidAfter(serviceArg, SFARestHeaderUtil.getCrmHeader(context.getTenantId(), context.getUser()));
        SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                .parameters(JSON.toJSONString(serviceArg))
                .action(ObjectAction.INVALID.getActionCode())
                .extra("salesOrderBizProxy.invalidAfter")
                .objectApiNames(Utils.SALES_ORDER_API_NAME)
                .objectIds(data.getId())
                .message(JSON.toJSONString(invalidAfterResult))
                .build(), context.getRequestContext());
        if (!invalidAfterResult.isSuccess()) {
            throw new ValidateException(invalidAfterResult.getMessage());
        }
    }

    private void findNewInvoiceLineByOrderId(String orderId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(10);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "order_id", orderId);
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        SearchUtil.fillFilterIn(filters, "life_status", Lists.newArrayList(
            SystemConstants.LifeStatus.UnderReview.value,
            SystemConstants.LifeStatus.Normal.value,
            SystemConstants.LifeStatus.InChange.value));
        query.setFilters(filters);
        QueryResult<IObjectData> newInvoiceLines = serviceFacade.findBySearchQuery(actionContext.getUser(), Utils.INVOICE_APPLICATION_LINES_API_NAME, query);
        if (CollectionUtils.notEmpty(newInvoiceLines.getData())) {
            List<IObjectData> data = newInvoiceLines.getData();
            if (data.size() > 0) {
                throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
            }
        }
    }
}
