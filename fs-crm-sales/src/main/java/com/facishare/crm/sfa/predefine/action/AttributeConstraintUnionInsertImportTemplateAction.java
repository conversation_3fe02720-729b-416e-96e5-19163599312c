package com.facishare.crm.sfa.predefine.action;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.crm.sfa.utilities.util.i18n.AttributeConstraintI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class AttributeConstraintUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    private static final List<String> toRemoveField = Lists.newArrayList(AttributeConstaintLinesConstants.FIELD_NODE_LEVEL, AttributeConstaintLinesConstants.FIELD_ROOT_NODE_ID);
    @Override
    protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
        super.customDetailHeader(detailFieldList);
        Optional<IFieldDescribe> parent = detailFieldList.stream().filter(x-> Objects.equals(AttributeConstaintLinesConstants.FIELD_PARENT_ID, x.getApiName())).findFirst();
        if(parent.isPresent()) {
            IFieldDescribe parentField = parent.get();
            //先移除
            detailFieldList.remove(parentField);
            //再加入，目的是调整位置
            detailFieldList.add(1, parentField);
        }
        detailFieldList.removeIf(x->toRemoveField.contains(x.getApiName()));
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_ID));
        map.put("api_name", AttributeConstaintLinesConstants.FIELD_NODE_TEMP_ID);
        IFieldDescribe field = ImportExportExt.createField(map, "SFA_RELATED_MARK", true);
        detailFieldList.add(1, field);
    }

    @Override
    protected String getFieldSampleValue(IFieldDescribe field) {
        if(Objects.equals(AttributeConstaintLinesConstants.FIELD_NODE_TYPE, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_TYPE);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_LABEL);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_VALUE_IDS, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_VALUE_LABEL);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_DEFAULT_ATTR_VALUES_IDS, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_DEFAULT_VALUE_LABEL);
        } else {
            return super.getFieldSampleValue(field);
        }
    }
}
