package com.facishare.crm.sfa.predefine.service.accountsreceivable.dto;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Data
public class AccountsReceivableType {

    public static enum AccountsReceivableEnableSwitchStatus {
        UNABLE(0, "未开启"), OPENING(1, "开启中"), ENABLE(2, "已经开启"), FAILED(3, "开启失败");

        private String label;
        private int value;

        AccountsReceivableEnableSwitchStatus(int value, String label) {
            this.label = label;
            this.value = value;
        }

        public static AccountsReceivableEnableSwitchStatus valueOf(int value) {
            for (AccountsReceivableEnableSwitchStatus status : values()) {
                if (status.getValue() == value) {
                    return status;
                }
            }
            return null;
        }

        public String getLabel() {
            return label;
        }

        public int getValue() {
            return value;
        }
    }


    @Data
    public static class EnableAccountsReceivableResult {
        /**
         * 0 未开启
         * 1 开启中
         * 2 已经开启
         * 3 开启失败
         */
        private int enableStatus;
        private String message;
    }

    @Data
    public static class GetConfig {
        @Data
        public static class Arg {
            private List<String> keys;
        }

        @Data
        public static class Result {
            private List<HashResult> values;
        }

        @Data
        public static class HashResult {
            private String key;
            private Object value;
        }
    }


    @Data
    public static class UpdateConfig {
        @Data
        public static class Arg {
            private String key;
            private Object value;
        }

        @Data
        public static class Result {
        }
    }

    @Data
    public static class AddRuntimeConfig {
        @Data
        public static class Arg {
            private List<String> tenantIds;
        }

        @Data
        public static class Result {
        }
    }

    @Data
    @Builder
    public static class AccountsReceivableDetailData {
        private int count;
        private int removeCount;
        private int otherCount;
        private List<Map<String, Object>> map;
    }



    @Data
    public static class UpdateArDetailPriceTaxAmount {
        @Data
        public static class Arg {
            private List<String> tenantIds;
        }

        @Data
        public static class Result {
        }
    }

    @Data
    public static class UpdateOrderProductIdWhere {
        @Data
        public static class Arg {
            private List<String> tenantIds;
        }

        @Data
        public static class Result {
        }
    }

    @Data
    public static class AutoMatchReceivable {
        @Data
        public static class Arg {
            private String accountId;
            private BigDecimal amount;
        }

        @Data
        @Builder
        public static class Result {
            private List<ObjectDataDocument> arDatas;
        }
    }

    @Data
    public static class BulkCreateMatchNote {
        @Data
        public static class Arg {
            private List<Map> check_match_datas;
            private String paymentId;
            private DebitMatchModel debitMatchData;
            private String matchDataDescribeApiName;
            private String matchDataDetailDescribeApiName;
        }

        @Data
        @Builder
        public static class Result {
        }
        @Data
        public static class DebitMatchModel {
            private String dataId;
            private String describeApiName;
        }
    }

    @Data
    public static class CheckPaymentMatchNote {
        @Data
        public static class Arg {
            private ObjectDataDocument payment;
        }

        @Data
        @Builder
        public static class Result {
            private Boolean result;
        }
    }
    @Data
    public static class AutoMatchNote {
        @Data
        public static class Arg {
            private String objectDescribeApiName;
            private String objectDataId;
        }

        @Data
        @Builder
        public static class Result {
            private Boolean result;
        }
    }
    @Data
    public static class PeriodicAccountsReceivable {
        @Data
        public static class Arg {
            private String masterDescribeApiName;
            private String detailDescribeApiName;
            private String detailDataId;
        }

        @Data
        @Builder
        public static class Result {
            private Boolean result;
        }
    }
    @Data
    public static class GetSettlementRulesPreData {
        @Data
        public static class Arg {
            private String objectDataId;
        }

        @Data
        @Builder
        public static class Result {
            private ObjectDataDocument data;
            private IObjectDescribe describe;
        }
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CommonResult {
        private boolean isSuccess;
    }

    @Data
    public static class ModifyPeriodicAccountsReceivable {
        @Data
        public static class Arg {
            private ObjectDataDocument detailData;
        }
    }

    @Data
    public static class ArQuickRuleModel {
        @Data
        public static class CreateOrUpdateArg {
            private String id;
            private String name;
            private String objectApiName;
            private String conditions;
            private Integer maxPeriodCount;
            private String minimumAmountCondition;
            private List<String> informationFields;
        }

        @Data
        public static class ListArg {
            private String objectApiName;
        }

        @Data
        @Builder
        public static class ListResult {
            private List<ObjectDataDocument> ruleDatas;
        }
        @Data
        public static class DeleteArg {
            private String id;
        }

        @Data
        public static class CheckArQuickArg {
            private String objectId;
            private String describeApiName;
        }
        @Data
        @Builder
        public static class CheckArQuickResult {
            private String accounts_receivable_quick_rule_id;
            private Integer min_amount;
            private boolean is_last_period;
        }
    }
}