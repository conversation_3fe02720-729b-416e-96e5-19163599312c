package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.*;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.dto.FieldResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.crm.privilege.model.valueobject.CrmResult.SUCCESS;

/**
 * Created by luxin on 2018/10/29.
 */
@ServiceModule("spu_sku_object")
@Component
@Slf4j
public class SpuSkuService {

    @Autowired
    DhtPriceBookServiceCopy dhtPriceBookService;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private UnitService unitService;



    private static Integer batchUpdatePicturesLimit;
    //总的更新数量
    private static Integer batchUpdatePicturesPictureTotalNumberLimit;

    static {
        ConfigFactory.getConfig("fs-crm-java-config", config -> {
            batchUpdatePicturesLimit = config.getInt("batchUpdatePicturesLimit", 20);
            batchUpdatePicturesPictureTotalNumberLimit = config.getInt("batchUpdatePicturesPictureTotalNumberLimit", 500);
        });
    }

    //根据spu [价目表id] [isIncludeAll]来筛选spec&spec_value
    @ServiceMethod("search_spec_and_values_by_spu")
    public SearchUsedSpecModel.Result searchUsedSpecValuesBySpuId(SearchUsedSpecModel.Arg arg, ServiceContext context) {
        String sql;
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(context.getTenantId())) {
            arg.setPriceBookId(null);
        }
        if (Strings.isNotBlank(arg.getPriceBookId()) && !dhtPriceBookService.priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getNewProducts priceBook is not available arg:{}", arg);
            return new SearchUsedSpecModel.Result();
        } else {
            arg.setIsAddProToPrice(arg.getIsAddProToPrice() == null ? Boolean.FALSE : arg.getIsAddProToPrice());
            if (GrayUtil.splitWhere(context.getTenantId())) {
                sql = ConcatenateSqlUtils.getSpecAndValueSqlSplitWhere(arg, context.getTenantId());
            } else {
                sql = ConcatenateSqlUtils.getSpecAndValueSql(arg, context.getTenantId());
            }
        }
        sql = grayQueryLimit(context, sql);
        QueryResult<IObjectData> dataResult;
        try {
            dataResult = objectDataService.findBySql(sql, context.getTenantId(), "SpuSkuSpecValueRelateObj");
        } catch (MetadataServiceException e) {
            log.error("SpuSkuService searchUsedSpecValuesBySpuId findBySql error. sql:{} ", sql);
            throw new APPException("system error.");
        }
        if (GrayUtil.splitWhere(context.getTenantId())) {
            filterDataResult(dataResult, arg, context);
        }
        Map<String, SearchUsedSpecModel.SpecAndSpecValue> specIdtoSpec = Maps.newHashMap();
        List<String> orderBySpec = Lists.newArrayList();
        dataResult.getData().forEach(o -> {
            if (Strings.isBlank(o.get("used", String.class)) && !arg.getIsIncludeAll()) {
                return;
            }
            String specId = o.get("spec_id", String.class);
            if (!orderBySpec.contains(specId)) {
                orderBySpec.add(specId);
            }
            specIdtoSpec.computeIfAbsent(specId, k -> SearchUsedSpecModel.SpecAndSpecValue.builder().specId(specId).build());
            SearchUsedSpecModel.SpecAndSpecValue specAndSpecValue = specIdtoSpec.get(specId);
            if (Strings.isBlank(specAndSpecValue.getSpecName())) {
                specAndSpecValue.setSpecName(o.get("spec_name", String.class));
            }
            specAndSpecValue.setActive("1".equals(o.get("spec_status", String.class)) ? Boolean.TRUE : Boolean.FALSE);
            SearchUsedSpecModel.SpecValue specValue = SearchUsedSpecModel.SpecValue.builder().specValueId(o.get("spec_value_id", String.class)).specValueName(o.get("spec_value_name", String.class)).active("1".equals(o.get("spec_value_status", String.class)) ? Boolean.TRUE : Boolean.FALSE)
                    //跟前端规定了，0是未使用，1是被使用
                    .status(arg.getIsIncludeAll() ? (Strings.isBlank(o.get("used", String.class)) ? "0" : "1") : null).build();
            if (!specAndSpecValue.getSpecValueList().contains(specValue)) {
                specAndSpecValue.getSpecValueList().add(specValue);
            }
        });
        List<SearchUsedSpecModel.SpecAndSpecValue> specAndSpecValueList = Lists.newArrayList();
        orderBySpec.forEach(o -> specAndSpecValueList.add(specIdtoSpec.get(o)));
        return SearchUsedSpecModel.Result.builder().dataList(specAndSpecValueList).build();
    }

    private String grayQueryLimit(ServiceContext context, String sql) {
        if (GrayUtil.limitSpec(context.getTenantId())) {
            sql += " limit 2000";
        }
        return sql;
    }

    /**
     * 规格产品过多时,在where中拼价目表过滤条件,效率有点低,拆出来在代码中过滤
     *
     * @param dataResult 数据结果
     * @param arg        参数
     * @param context    上下文
     */
    private void filterDataResult(QueryResult<IObjectData> dataResult, SearchUsedSpecModel.Arg arg, ServiceContext context) {
        if (arg.getIsIncludeAll()) {
            return;
        }
        //添加价目表,不过滤价目表id
        if (!arg.getIsAddProToPrice()) {
            //过滤价目表中的skuId
            if (StringUtils.isNotBlank(arg.getPriceBookId())) {
                try {
                    List<Map> productIdMap = objectDataService.findBySql(context.getTenantId(), ConcatenateSqlUtils.getProductIdByPriceBookId(context.getTenantId(), arg.getPriceBookId(), arg));
                    if (CollectionUtils.isNotEmpty(productIdMap)) {
                        Set<String> productIdSet = productIdMap.stream().map(o -> o.get("product_id")).filter(Objects::nonNull).map(Object::toString)
                                .collect(Collectors.toSet());
                        Optional.ofNullable(dataResult)
                                .map(QueryResult::getData)
                                .ifPresent(data -> {
                                    data.removeIf(o -> !productIdSet.contains(o.get("sku_id", String.class)));
                                });
                    }
                } catch (MetadataServiceException e) {
                    log.error("SpuSkuService searchUsedSpecValuesBySpuId findBySql error. sql:{} ", ConcatenateSqlUtils.getProductIdByPriceBookId(context.getTenantId(), arg.getPriceBookId(), arg));
                    throw new APPException("system error.");
                }
            }
        }
        //在价目表中添加,过滤skuId not in skuIdsToFilter
        containsSkuIdsRemove(dataResult, arg);
    }

    private void containsSkuIdsRemove(QueryResult<IObjectData> dataResult, SearchUsedSpecModel.Arg arg) {
        if (CollectionUtils.isNotEmpty(arg.getSkuIdsToFilter())) {
            List<String> skuIdsToFilter = arg.getSkuIdsToFilter();
            Optional.ofNullable(dataResult)
                    .map(QueryResult::getData)
                    .ifPresent(data -> {
                        data.removeIf(o -> skuIdsToFilter.contains(o.get("sku_id", String.class)));
                    });
        }
    }


    @ServiceMethod("search_skulist_by_spu")
    public Object searchSkuListBySpu() {
        return null;
    }

    //分类删除的时候校验分类是否被商品引用
    @ServiceMethod("check_delete_category")
    public CheckDeleteCategoryModel.Result checkDeleteCategory(CheckDeleteCategoryModel.Arg arg, ServiceContext context) {
        return checkDeleteCategory(context.getUser(), arg.getCategoryCodes());
    }

    public CheckDeleteCategoryModel.Result checkDeleteCategory(User user, List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new CheckDeleteCategoryModel.Result(Boolean.FALSE);
        }
        QueryResult<IObjectData> dataResult;
        String sql = "";
        String apiName = Utils.SPU_API_NAME;
        // 商品产品解藕。如果是有商品，走商品的校验，如果没有，走产品的。
        if (SFAConfigUtil.isSpuOpen(user.getTenantId())) {
            sql = ConcatenateSqlUtils.checkCategory(user.getTenantId(), codes);
        } else {
            sql = ConcatenateSqlUtils.checkProductCategory(user.getTenantId(), codes);
            apiName = Utils.PRODUCT_API_NAME;
        }
        try {
            log.info("checkDeleteCategory#SQL:{}", sql);
            dataResult = objectDataService.findBySql(sql, user.getTenantId(), apiName);
            if (!dataResult.getData().isEmpty()) {
                return new CheckDeleteCategoryModel.Result(Boolean.FALSE);
            } else {
                return new CheckDeleteCategoryModel.Result(Boolean.TRUE);
            }
        } catch (MetadataServiceException e) {
            log.error("SpuSkuService searchUsedSpecValuesBySpuId findBySql error. sql:{} ", sql);
            throw new APPException("system error.");
        }
    }


    @ServiceMethod("batch_get_sku_by_spuIds")
    public SpuInfoModel.Result batchGetSkuBypuIds(SpuInfoModel.Arg arg, ServiceContext context) {
        log.info("batchGetSkuBypuIds begin,arg {},context {}", arg, context);
        if (!SFAConfigUtil.isSpuOpen(context.getTenantId())) {
            return SpuInfoModel.Result.builder().spuInfoList(Lists.newArrayList()).build();
        }
        try {
            QueryResult<IObjectData> spuListResult = getSpuListResult(arg, context);
            List<IObjectData> spuList = spuListResult.getData();

            if (CollectionUtils.isEmpty(spuList)) {
                return SpuInfoModel.Result.builder().spuInfoList(Collections.EMPTY_LIST).build();
            }

            List<String> spuIds = Lists.newArrayList(spuList.stream().map(IObjectData::getId).collect(Collectors.toSet()));
            QueryResult<IObjectData> skuListResult = getSkuListResult(arg, context, spuIds);

            List<IObjectData> skuList = skuListResult.getData();
            skuList.forEach(this::handleProductSpec);
            Map<String, List<IObjectData>> spuIdSkuMap;
            spuIdSkuMap = skuList.stream().collect(Collectors.toMap(key -> key.get("spu_id", String.class), value -> Lists.newArrayList(value), (List<IObjectData> newValueList, List<IObjectData> oldValueList) -> {
                oldValueList.addAll(newValueList);
                return oldValueList;
            }));

            for (IObjectData spuData : spuList) {
                spuData.set("skuList", ObjectDataDocument.ofList(spuIdSkuMap.get(spuData.getId())));
            }
            return SpuInfoModel.Result.builder().errorCode("0").message("batchGetSkuBypuIds success").spuInfoList(ObjectDataDocument.ofList(spuList)).build();
        } catch (Exception e) {
            log.warn("batchGetSkuBypuIds failed,arg {},context {}", arg, context, e);
            return SpuInfoModel.Result.builder().errorCode("1").message("batchGetSkuBypuIds failed").build();
        }
    }

    @ServiceMethod("paging_get_spu_sku_info")
    public Object pagingGetSpuSkuInfo(SpuSkuInfo.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        if (!SFAConfigUtil.isSpuOpen(tenantId)) {
            return Lists.newArrayList();
        }
        checkSpuSkuInfoArg(arg);
        SearchTemplateQueryExt ext = generateSearchQuery(context.getUser(), arg.getOffset(), arg.getLimit(), arg.getOrderByList());

        ActionContext actionContext = ContextConvertUtils.serviceContext2IActionContext(context);
        QueryResult<IObjectData> spuQueryResult = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, Utils.SPU_API_NAME, ext.toSearchTemplateQuery(), SupportCheckInsSpuSkuUtils.pagingSpuFields());

        List<IObjectData> spuDataList = spuQueryResult.getData();
        Set<String> spuIds = spuQueryResult.getData().stream().map(IObjectData::getId).collect(Collectors.toSet());

        if (!spuIds.isEmpty()) {
            SearchTemplateQueryExt searchTemplateQueryExt = generateSearchQuery(context.getUser(), "spu_id", Lists.newArrayList(spuIds));
            int cycles = 2;
            String cyclesStr = bizConfigThreadLocalCacheService.getBizConfig(tenantId, "custom_cycles");
            if (StringUtils.isNotEmpty(cyclesStr)) {
                cycles = Integer.parseInt(cyclesStr);
            }
            List<IObjectData> dataList = CommonSearchUtil.findBySearchQueryWithFields(actionContext, Utils.PRODUCT_API_NAME, searchTemplateQueryExt.toSearchTemplateQuery(), SupportCheckInsSpuSkuUtils.pagingSkuFields(), cycles);
            Map<String, List<IObjectData>> spuId2SkuData = dataList.stream().collect(Collectors.groupingBy(o -> (String) o.get("spu_id")));
            List<IObjectData> skuDataList;
            for (IObjectData spuData : spuDataList) {
                skuDataList = spuId2SkuData.get(spuData.getId());
                if (CollectionUtils.isNotEmpty(skuDataList)) {
                    skuDataList.forEach(this::handleProductSpec);
                    spuData.set("skuList", ObjectDataDocument.ofList(skuDataList));
                }
            }
            return ObjectDataDocument.ofList(spuDataList);
        }
        return Lists.newArrayList();
    }

    private void handleProductSpec(IObjectData objectData) {
        String productSpec = objectData.get("product_spec", String.class);
        if (StringUtils.isNotBlank(productSpec)) {
            StringBuilder builder = new StringBuilder();
            String[] specList = productSpec.split(";");
            for (String spec : specList) {
                String[] specInfo = spec.split(":");
                if (specInfo.length == 2) {
                    builder.append(specInfo[1]).append("/");
                }
            }
            String productSpecResult = builder.length() > 0 ? builder.substring(0, builder.length() - 1) : "";
            objectData.set("product_spec", productSpecResult);
        }
    }

    private void checkSpuSkuInfoArg(SpuSkuInfo.Arg arg) {
        if (arg.getLimit() >= 1000) {
            //throw new ValidateException("limit 不能大于1000");
            throw new ValidateException(I18N.text("sfa.limit.cannot.dayu.yiqian"));
        }

        if (arg.getLimit() <= 0 || arg.getOffset() < 0) {
            //throw new ValidateException("分页参数不正确");
            throw new ValidateException(I18N.text("sfa.fenye.parameter.error"));
        }
    }

    private QueryResult<IObjectData> getSkuListResult(SpuInfoModel.Arg arg, ServiceContext context, List<String> spuIds) {
        QueryResult<IObjectData> skuListResult;
        try {
            SearchTemplateQueryExt ext = generateSearchQuery(context.getUser(), "spu_id", spuIds);
            skuListResult = serviceFacade.findBySearchQuery(context.getUser(), SystemConstants.ProductApiName, (SearchTemplateQuery) ext.getQuery());
        } catch (Exception e) {
            log.warn("getSkuList failed,arg {},context {}", arg, context, e);
            throw new APPException("getSkuList failed", e);
        }
        return skuListResult;
    }

    private void setFilterDataRight(SearchTemplateQueryExt ext) {
        ext.setPermissionType(1);
        IDataRightsParameter dataRightsParameter = new DataRightsParameter();
        dataRightsParameter.setRoleType("1");
        dataRightsParameter.setSceneType("all");
        dataRightsParameter.setCascadeDept(true);
        dataRightsParameter.setCascadeSubordinates(true);
        ext.setDataRightsParameter(dataRightsParameter);
    }

    private QueryResult<IObjectData> getSpuListResult(SpuInfoModel.Arg arg, ServiceContext context) {
        QueryResult<IObjectData> spuListResult;
        try {
            SearchTemplateQueryExt ext = generateSearchQuery(context.getUser(), DBRecord.ID, arg.getSpuIdList());
            spuListResult = serviceFacade.findBySearchQuery(context.getUser(), SystemConstants.SPUApiName, (SearchTemplateQuery) ext.getQuery());
            return spuListResult;
        } catch (Exception e) {
            log.warn("getSpulist failed,arg {},context {}", arg, context, e);
            throw new APPException("getSpuList failed", e);
        }
    }

    @NotNull
    private SearchTemplateQueryExt generateSearchQuery(User user, String id, List<String> spuIdList) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.setOffset(0);
        ext.setLimit(1000);
        ext.addFilter(Operator.IN, id, spuIdList);
        ext.addFilter(Operator.EQ, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        if (serviceFacade.isAdmin(user)) {
            ext.setPermissionType(0);
        } else {
            setFilterDataRight(ext);
        }
        List<OrderBy> orderByList = Lists.newArrayList();
        OrderBy orderBy = new OrderBy(DBRecord.ID, true);
        orderByList.add(orderBy);
        ext.setOrders(orderByList);
        return ext;
    }

    private SearchTemplateQueryExt generateSearchQuery(User user, int offset, int limit, List<OrderBy> orderByList) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.setOffset(offset);
        ext.setLimit(limit);
        ext.addFilter(Operator.EQ, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        if (serviceFacade.isAdmin(user)) {
            ext.setPermissionType(0);
        } else {
            setFilterDataRight(ext);
        }
        if (com.facishare.paas.appframework.common.util.CollectionUtils.size(orderByList) > 0) {
            ext.setOrders(orderByList);
        }
        return ext;
    }

    /**
     * 开了订货通的企业，需要批量更新图片
     */
    @ServiceMethod("batch_update_pictures")
    public BatchUpdatePicturesModel.Result batchUpdatePictures(ServiceContext context, BatchUpdatePicturesModel.Arg arg) {
        // 是否有编辑权限
        this.serviceFacade.doFunPrivilegeCheck(context.getUser(), arg.getObjectApiName(), StandardAction.Edit.getFunPrivilegeCodes());

        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            if (picturePath.getPicturePath() == null) {
                picturePath.setPicturePath(Lists.newArrayList());
            }
        }

        if (arg.getPicturePaths().size() > batchUpdatePicturesLimit) {
            String msg = String.format(I18N.text(SalesOrderI18NKeyUtil.DATA_NUMBER_MORE_THAN_LIMIT), batchUpdatePicturesLimit);
            throw new ValidateException(msg);
        }

        String pathFieldApiName = null;
        if (Objects.equals(arg.getObjectApiName(), "ProductObj")) {
            pathFieldApiName = "picture_path";
        } else if (Objects.equals(arg.getObjectApiName(), "SPUObj")) {
            pathFieldApiName = "picture";
        } else {
            throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.OBJECT_NOT_SUPPORT_BATCH_UPDATE_PICTURE));
        }

        //更新spu，可能还需要更新sku
        List<IObjectData> needUpdateSkus = getNeedUpdateSkus(context.getUser(), arg);

        //总量500
        int totalUpdateNum = 0;
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            totalUpdateNum = totalUpdateNum + picturePath.getPicturePath().size();
        }
        totalUpdateNum = totalUpdateNum + needUpdateSkus.size();
        if (totalUpdateNum > batchUpdatePicturesPictureTotalNumberLimit) {
            throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.DATA_NUMBER_UPDATE_PICTURE_NUMBER_MORE_THAN_LIMIT, batchUpdatePicturesPictureTotalNumberLimit));
        }

        //字段上的数量限制
        boolean needUpdateSkuPic = !CollectionUtils.isEmpty(needUpdateSkus);
        checkPicNumberLimit(context.getUser(), arg, pathFieldApiName, needUpdateSkuPic);

        String tenantId = context.getTenantId();
        List<IObjectData> dataList = new ArrayList<>();
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            IObjectData data = new ObjectData();
            data.setId(picturePath.getDataId());
            data.set(pathFieldApiName, picturePath.getPicturePath());
            data.setDescribeApiName(arg.getObjectApiName());
            data.setTenantId(tenantId);
            dataList.add(data);
        }

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        List<String> updateFieldList = Lists.newArrayList(pathFieldApiName);
        String errMessage = batchUpdateByFields(context.getUser(), arg.getObjectApiName(), dataList, updateFieldList, arg, parallelTask);
        if (errMessage != null) {
            return BatchUpdatePicturesModel.Result.builder().errorCode("1").message(errMessage).build();
        }

        //更新spu，可能还需要更新sku
        updateFieldList = Lists.newArrayList("picture_path");
        errMessage = batchUpdateByFields(context.getUser(), "ProductObj", needUpdateSkus, updateFieldList, arg, parallelTask);
        if (errMessage != null) {
            return BatchUpdatePicturesModel.Result.builder().errorCode("1").message(errMessage).build();
        }

        try {
            parallelTask.await(20, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("SpuSkuService add log time out", e);
        }
        return BatchUpdatePicturesModel.Result.builder().errorCode("0").build();
    }

    private String batchUpdateByFields(User user, String objectApiName, List<IObjectData> dataList, List<String> updateFieldList, BatchUpdatePicturesModel.Arg arg, ParallelUtils.ParallelTask parallelTask) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        try {
            serviceFacade.batchUpdateByFields(user, dataList, updateFieldList);

            //修改记录
            List<String> dataIds = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            recordLog(user, objectApiName, dataIds, parallelTask);
            return null;
        } catch (Exception e) {
            log.warn("batchUpdateByFields failed, user[{}], dataList[{}], updateFieldList[{}]", updateFieldList, dataList, updateFieldList);
            //Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: value too long for type character varying(2000)
            if (e.getMessage() != null && e.getMessage().contains("value too long for type character varying")) {
                //数据最长的一个
                String maxPathDataName = getMaxPathDataName(user, objectApiName, arg);
                return I18N.text(SalesOrderI18NKeyUtil.UPDATE_PICTURE_FAIL) + ", " + String.format(I18N.text(SalesOrderI18NKeyUtil.REDUCE_PICTURE_NUMBER_AND_RETRY), maxPathDataName);
            }
            return I18N.text(SalesOrderI18NKeyUtil.UPDATE_PICTURE_FAIL) + e.getMessage();
        }
    }

    private String getMaxPathDataName(User user, String objectApiName, BatchUpdatePicturesModel.Arg arg) {
        String maxDataId = null;
        int maxLength = 0;
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            int length = picturePath.getPicturePath() == null ? 0 : picturePath.getPicturePath().toString().length();
            if (length > maxLength) {
                maxDataId = picturePath.getDataId();
                maxLength = length;
            }
        }

        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(maxDataId), objectApiName);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0).getName();
    }

    /**
     * 更新spu的时候，勾选了更新sku
     */
    private List<IObjectData> getNeedUpdateSkus(User user, BatchUpdatePicturesModel.Arg arg) {
        List<IObjectData> dataList = new ArrayList<>();

        if (!Objects.equals(arg.getObjectApiName(), "SPUObj")) {
            return dataList;
        }

        //需要更新sku的spuId
        List<String> needUpdateSkuSpuIds = Lists.newArrayList();
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            if (Objects.equals(picturePath.isUpdateSkuPic(), true)) {
                needUpdateSkuSpuIds.add(picturePath.getDataId());
            }
        }

        if (CollectionUtils.isEmpty(needUpdateSkuSpuIds)) {
            return dataList;
        }

        //需要更新的所有的skuIds
        List<String> fields = Lists.newArrayList("spu_id", DBRecord.ID);
        List<IObjectData> products = ProductUtils.getProducts(user, needUpdateSkuSpuIds, fields);
        if (CollectionUtils.isEmpty(products)) {
            return dataList;
        }


        Map<String, BatchUpdatePicturesModel.PicturePathList> spuId2Picture = arg.getPicturePaths().stream().collect(Collectors.toMap(BatchUpdatePicturesModel.PicturePathList::getDataId, p -> p));

        for (IObjectData product : products) {
            String spuId = (String) product.get("spu_id");

            IObjectData data = new ObjectData();
            data.setId(product.getId());
            data.set("picture_path", spuId2Picture.containsKey(spuId) ? spuId2Picture.get(spuId).getPicturePath() : Lists.newArrayList());
            data.setDescribeApiName("ProductObj");
            data.setTenantId(user.getTenantId());
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 参考 PriceBookProductBulkEditAction
     * 中保存log com.facishare.crm.sfa.predefine.action.BaseObjectBulkSaveAction.logAsync
     * <p>
     * 没用  public void log(User user, EventType eventType, ActionType actionType, IObjectDescribe objectDescribe, IObjectData data, Map<String, Object> updatedFieldMap, IObjectData dbData)
     * 因为没有批量接口
     */
    private void recordLog(User user, String objectApiName, List<String> dataIds, ParallelUtils.ParallelTask parallelTask) {
        parallelTask.submit(() -> {
            Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
            IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
            objectDescribes.put(objectApiName, objectDescribe);

            List<IObjectData> dataList = serviceFacade.findObjectDataByIds(user.getTenantId(), dataIds, objectApiName);
            serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, objectDescribes, dataList);
        });
    }

    private void checkPicNumberLimit(User user, BatchUpdatePicturesModel.Arg arg, String pathFieldApiName, boolean needUpdateSkuPic) {
        checkPicNumberLimit(user, arg, pathFieldApiName, arg.getObjectApiName(), false);

        if (needUpdateSkuPic) {
            pathFieldApiName = "picture_path";
            checkPicNumberLimit(user, arg, pathFieldApiName, "ProductObj", true);
        }
    }

    private void checkPicNumberLimit(User user, BatchUpdatePicturesModel.Arg arg, String pathFieldApiName, String objectApiName, Boolean needCheckSkuPicIsRequired) {
        FieldResult fieldResult = serviceFacade.findCustomFieldDescribe(user.getTenantId(), objectApiName, pathFieldApiName);
        IFieldDescribe picturePathField = fieldResult.getField();
        int fileAmountLimit = picturePathField.get("file_amount_limit", Integer.class);
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            if (picturePath.getPicturePath().size() > fileAmountLimit) {
                String msg = String.format(I18N.text(SalesOrderI18NKeyUtil.PICTURE_NUMBER_MORE_THAN_LIMIT), fileAmountLimit);
                throw new ValidateException(msg);
            }
        }

        //更新spu，选择更新sku，spu产品图片为空，sku图片是必填，报错
        if (!needCheckSkuPicIsRequired) {
            return;
        }
        // 是否有选择'同步至产品图片'的
        boolean hasUpdateSkuPic = false;
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            if (picturePath.isUpdateSkuPic()) {
                hasUpdateSkuPic = true;
                break;
            }
        }
        if (!hasUpdateSkuPic) {
            return;
        }

        Boolean isRequired = picturePathField.get("is_required", Boolean.class);
        if (!isRequired) {
            return;
        }
        for (BatchUpdatePicturesModel.PicturePathList picturePath : arg.getPicturePaths()) {
            if (CollectionUtils.isEmpty(picturePath.getPicturePath())) {
                throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SKU_PICTURE_PATH_IS_REQUIRED_SPU_PICTURE_MUST_NOT_EMPTY));
            }
        }
    }
    @ServiceMethod("synchronizeUnitForce")
    public CrmResult synchronizeUnitForce(ServiceContext context, SynchronizeUnitCustomerArg.Arg arg) {
        if (bizConfigThreadLocalCacheService.isMultipleUnit(context.getTenantId())) {
            unitService.synchronizeUnitForce(context.getUser(), arg.getDescribeApiNames());
        }
        return new CrmResult(SUCCESS);
    }
}