package com.facishare.crm.sfa.predefine.service.accountsreceivable.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ObjectArRuleRelatedConstants;
import com.facishare.crm.sfa.utilities.constant.SaleActionNewConstants;
import com.facishare.crm.sfa.utilities.util.OpportunityUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ObjectArRuleRelatedDao {

    @Resource
    private ServiceFacade serviceFacade;

    private static final int MAX_BATCH_SIZE = 2000;
    public List<IObjectData> getDataListByObjectIdApiName(User user, String objectApiName, String objectId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ObjectArRuleRelatedConstants.OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectArRuleRelatedConstants.OBJECT_ID, objectId);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setLimit(MAX_BATCH_SIZE);
        return serviceFacade.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ObjectArRuleRelated.getApiName(), query).getData();
    }

    public List<IObjectData> getUnCompleteDataListByRuleId(User user, String ruleId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ObjectArRuleRelatedConstants.ACCOUNTS_RECEIVABLE_QUICK_RULE_ID, ruleId);
        SearchUtil.fillFilterNEq(filters, ObjectArRuleRelatedConstants.IS_COMPLETED, true);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        return serviceFacade.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ObjectArRuleRelated.getApiName(), query).getData();
    }
}
