package com.facishare.crm.sfa.predefine.action;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.i18n.AttributeConstraintI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportVerifyAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class AttributeConstraintLinesUnionInsertImportVerifyAction extends StandardUnionInsertImportVerifyAction {

    private static final List<String> toRemoveField = Lists.newArrayList(AttributeConstaintLinesConstants.FIELD_NODE_LEVEL, AttributeConstaintLinesConstants.FIELD_ROOT_NODE_ID);

    @Override
    protected void doFunPrivilegeCheck() {
        return;
    }

    @Override
    protected void doDataPrivilegeCheck() {
        return;
    }

    @Override
    protected void supportUniqueID(List<IFieldDescribe> fieldDescribeList) {
        return;
    }

    @Override
    protected List<IFieldDescribe> customHandleFields(List<IFieldDescribe> fieldDescribeList) {
        List<IFieldDescribe> fieldDescribes = super.customHandleFields(fieldDescribeList);
        fieldDescribes.removeIf(field-> toRemoveField.contains(field.getApiName()));
        return fieldDescribes;
    }

    @Override
    protected void supportRelatedMark(List<IFieldDescribe> fieldDescribeList) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_ID));
        map.put("api_name", AttributeConstaintLinesConstants.FIELD_NODE_TEMP_ID);
        IFieldDescribe field = ImportExportExt.createField(map, "SFA_RELATED_MARK", true);
        fieldDescribeList.add(1, field);
    }
}
