package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.SFAButtonUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.util.CommonConfigCenter;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SalesOrderSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    @Override
    public String getApiName() {
        return Utils.SALES_ORDER_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.COLLECT));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIRM_DELIVERY));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIRM_RECEIPT));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIRM));
        buttons.add(ButtonUtils.buildButton(ObjectAction.REJECT));
        buttons.add(ButtonUtils.buildButton(ObjectAction.RECALL));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CHANGE_CONFIRMOR));
        buttons.add(ButtonUtils.buildButton(ObjectAction.ADD_DELIVERY_NOTE));
        buttons.add(ButtonUtils.buildButton(ObjectAction.PAY_INSTANTLY));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIRM_RECEIPT2));
        buttons.add(ButtonUtils.buildButton(ObjectAction.BUY_AGAIN));
        buttons.add(ButtonUtils.buildButton(ObjectAction.OnlinePayment));
        // 黑名单，不加
        if (CommonConfigCenter.needBalanceReduceTenant(RequestContextManager.getContext().getTenantId())) {
            buttons.add(ButtonUtils.buildButton(ObjectAction.BALANCE_REDUCE));
        }
        buttons.add(ButtonUtils.buildButton(ObjectAction.ONE_MORE_ORDER));

        if (SFAButtonUtil.getCostAssignButtonList(RequestContextManager.getContext().getTenantId())) {
            buttons.add(ButtonUtils.buildButton(ObjectAction.SALES_ORDER_COST_ASSIGN));
        }
        if (SFAConfigUtil.closeOrderEnable(RequestContextManager.getContext().getTenantId())) {
            buttons.add(ButtonUtils.buildButton(ObjectAction.CLOSE_SALES_ORDER));
        }
        buttons.add(ButtonUtils.buildButton(ObjectAction.FREEZE_INVENTORY_ADJUSTMENT));
        buttons.add(ButtonUtils.buildButton(ObjectAction.TRANSFER_ORDER));
        return buttons;
    }
}
