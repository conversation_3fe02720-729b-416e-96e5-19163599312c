
package com.facishare.crm.sfa.predefine.service.accountsreceivable.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.describebuilder.CurrencyFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.enums.AccountReceivableSwitchEnum;
import com.facishare.crm.sfa.predefine.enums.MatchWayEnum;
import com.facishare.crm.sfa.predefine.exception.AccountReceivableBusinessException;
import com.facishare.crm.sfa.predefine.exception.AccountReceivableErrorCode;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.FDFAttachment;
import com.facishare.crm.sfa.predefine.service.MultiSourceCommonService;
import com.facishare.crm.sfa.predefine.service.accountsreceivable.AccountsReceivableService;
import com.facishare.crm.sfa.predefine.service.accountsreceivable.dao.AccountsReceivableQuickRuleDao;
import com.facishare.crm.sfa.predefine.service.accountsreceivable.dao.ObjectArRuleRelatedDao;
import com.facishare.crm.sfa.predefine.service.accountsreceivable.dto.AccountsReceivableType;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.manager.*;
import com.facishare.crm.sfa.predefine.service.task.AccountsReceivableEnableEvent;
import com.facishare.crm.sfa.task.PeriodicAccountsReceivableTaskService;
import com.facishare.crm.sfa.task.AutoMatchNoteTaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.util.ArFieldWheresDescribeUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.crm.util.SearchTemplateQueryPlus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectHandlerService;
import com.facishare.paas.appframework.core.predef.service.dto.handler.UpsertHandlerDefinitionAndRuntimeConfig;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.DeleteRule;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.data.Where;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
@Data
public class AccountsReceivableServiceImpl extends ArCommonService implements AccountsReceivableService {
    @Resource
    private ArConfigManagerNew arConfigManagerNew;
    @Resource
    private ArCommonManagerNew arCommonManagerNew;
    @Resource
    private ArObjectDescribeManagerNew arObjectDescribeManagerNew;
    @Resource
    private ArInitObjManagerNew arInitObjManagerNew;
    @Resource
    private CommonDescribeManagerNew commonDescribeManagerNew;
    @Resource
    CommonConfigManagerNew commonConfigManagerNew;
    @Resource
    private ObjectHandlerService objectHandlerService;

    @Resource(name = "caMQSender")
    private AutoConfMQProducer rocketMQMessageSender;

    @Resource
    private InfraServiceFacade infraServiceFacade;

    @Resource
    private CommonAccountsReceivableNoteManagerNew commonAccountsReceivableNoteManagerNew;
    @Resource
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Resource
    private AutoMatchNoteTaskService autoMatchNoteTaskService;
    @Resource
    private MultiSourceCommonService multiSourceCommonService;
    @Resource
    private PeriodicAccountsReceivableTaskService periodicAccountsReceivableTaskService;
    @Resource
    private FDFAttachment fdfAttachment;
    @Resource
    private ArPaymentManagerNew arPaymentManagerNew;
    @Resource
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;
    @Resource
    private ObjectArRuleRelatedDao objectArRuleRelatedDao;
    @Resource
    private AccountsReceivableQuickRuleDao accountsReceivableQuickRuleDao;

    private static final String ACCOUNTS_RECEIVABLE_VERSION_KEY = "accounts_receivable_app";

    @Override
    public AccountsReceivableType.EnableAccountsReceivableResult enableAccountsReceivable(ServiceContext serviceContext) {
        long begin = System.currentTimeMillis();
        String tenantId = serviceContext.getTenantId();
        User user = serviceContext.getUser();

        AccountsReceivableType.EnableAccountsReceivableResult receivableResult = new AccountsReceivableType.EnableAccountsReceivableResult();
        Set<String> moduleList = serviceFacade.getModule(serviceContext.getTenantId());
        if (CollectionUtils.isEmpty(moduleList) || !moduleList.contains(ACCOUNTS_RECEIVABLE_VERSION_KEY)) {
            throw new ValidateException(I18N.text(ArI18NKey.CA_LICENSE_VALIDATE));
        }

        if (GrayUtil.grayPaymentAllowAmountLTZero(serviceContext.getTenantId())) {
            throw new ValidateException(I18N.text(ArI18NKey.PAYMENT_LT_ZERO));
        }
        if (bizConfigThreadLocalCacheService.isOpenOrderPaymentMultiSource(serviceContext.getTenantId())) {
            throw new ValidateException(I18N.text(ArI18NKey.PAYMENT_MULTI_SOURCE_NOT_OPEN_AR));
        }

        try {
            //初始化对象、回款加字段
            arInitObjManagerNew.init(tenantId, serviceContext.getUser());

            //打开开关
            arConfigManagerNew.updateAccountReceivableStatus(user, AccountReceivableSwitchEnum.OPENED);
            arConfigManagerNew.updateMatchWay(user, MatchWayEnum.AccountsReceivableDetail);
            arConfigManagerNew.openOpenArQuickAdd(user);
            arConfigManagerNew.openCreateArByObjects(user);
            addRuntimeConfig(serviceContext);
            // 支持项目管理，发送应收开启通知
            sendModuleEnableMq(tenantId);
            receivableResult.setEnableStatus(AccountReceivableSwitchEnum.OPENED.getStatus());
            receivableResult.setMessage(AccountReceivableSwitchEnum.OPENED.getMessage());
            log.info("init success, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin);
        } catch (Exception e) {
            log.error("init failed, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin, e);
            arConfigManagerNew.updateAccountReceivableStatus(user, AccountReceivableSwitchEnum.OPEN_FAIL);
            arConfigManagerNew.updateMatchWay(user, MatchWayEnum.AccountsReceivableDetail);
            //回滚系统库开放对象配置
            ArrayList<String> apiNames = Lists.newArrayList(
                    AccountsReceivableNoteObjConstants.API_NAME,
                    AccountsReceivableDetailObjConstants.API_NAME,
                    MatchNoteObjConstants.API_NAME,
                    MatchNoteDetailObjConstants.API_NAME,
                    //SalesInvoiceObjConstants.API_NAME,
                    //SalesInvoiceDetailObjConstants.API_NAME,
                    SettlementObjConstants.API_NAME,
                    SettlementDetailObjConstants.API_NAME
            );
            commonConfigManagerNew.updateMetaDataOpenConfig(serviceContext.getUser(), apiNames);
            receivableResult.setEnableStatus(AccountReceivableSwitchEnum.OPEN_FAIL.getStatus());
            receivableResult.setMessage(AccountReceivableSwitchEnum.OPEN_FAIL.getMessage());
            throw new AccountReceivableBusinessException(AccountReceivableErrorCode.INIT_DESCRIBE_PRIVILEGE_FAILED, I18N.text("AccountsReceivableNoteObj.errorinfo.AccountsReceivableServiceImpl.198") + e.getMessage());
        }
        return receivableResult;
    }

    /**
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
     */
    private void addRuntimeConfig(ServiceContext serviceContext) {
        /**
         * https://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
         * 4.2 预置业务 Handler 迁移 RoadMap
         * handler目前只支持 Add 和 EditAction
         *
         * 只迁移了 SalesOrderInterceptorService#editBefore 到 SalesOrderEditBeforeAccountsReceivableHandler，所以这里只有 "Edit"
         */
        HandlerRuntimeConfig config = new HandlerRuntimeConfig();
        config.setActive(true);
        config.setHandlerApiName("salesOrderEditBeforeAccountsReceivableHandler");
        config.setBindingObjectApiName("SalesOrderObj");
        config.setInterfaceCode("Edit");
        config.setExecuteOrder(199020);

        UpsertHandlerDefinitionAndRuntimeConfig.Arg arg = new UpsertHandlerDefinitionAndRuntimeConfig.Arg();
        arg.setRuntimeConfigs(Lists.newArrayList(config));
        UpsertHandlerDefinitionAndRuntimeConfig.Result result = objectHandlerService.upsertHandlerDefinitionAndRuntimeConfig(arg, serviceContext);
        log.info("objectHandlerService.upsertHandlerDefinitionAndRuntimeConfig arg[{}], result[{}]", arg, result);
    }

    @Override
    public AccountsReceivableType.AddRuntimeConfig.Result addRuntimeConfig(ServiceContext serviceContext, AccountsReceivableType.AddRuntimeConfig.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new AccountsReceivableType.AddRuntimeConfig.Result();
        }

        for (String tenantId : arg.getTenantIds()) {
            User user = new User(tenantId, "-10000");
            RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
            ServiceContext context = new ServiceContext(requestContext, null, null);
            addRuntimeConfig(context);
            log.info("addRuntimeConfig tenantId[{}]", tenantId);
        }

        return new AccountsReceivableType.AddRuntimeConfig.Result();
    }

    @Override
    public AccountsReceivableType.GetConfig.Result getConfig(ServiceContext serviceContext, AccountsReceivableType.GetConfig.Arg arg) {
        AccountsReceivableType.GetConfig.Result result = new AccountsReceivableType.GetConfig.Result();
        List<AccountsReceivableType.GetConfig.HashResult> values = new ArrayList<>();
        result.setValues(values);

        if (CollectionUtils.isEmpty(arg.getKeys())) {
            return result;
        }

        if (arg.getKeys().contains(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_SWITCH_KEY)) {
            AccountReceivableSwitchEnum switchStatus = arConfigManagerNew.getAccountReceivableStatus(serviceContext.getTenantId());

            AccountsReceivableType.GetConfig.HashResult hashResult = new AccountsReceivableType.GetConfig.HashResult();
            hashResult.setKey(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_SWITCH_KEY);
            hashResult.setValue(switchStatus.getStatus());
            values.add(hashResult);
        }

        if (arg.getKeys().contains(AccountsReceivableConstants.MATCH_WAY_KEY)) {
            MatchWayEnum matchWayEnum = arConfigManagerNew.getMatchWay(serviceContext.getTenantId());

            AccountsReceivableType.GetConfig.HashResult hashResult = new AccountsReceivableType.GetConfig.HashResult();
            hashResult.setKey(AccountsReceivableConstants.MATCH_WAY_KEY);
            hashResult.setValue(matchWayEnum.getStatus());
            values.add(hashResult);
        }

        return result;
    }

    @Override
    public AccountsReceivableType.UpdateConfig.Result updateConfig(ServiceContext serviceContext, AccountsReceivableType.UpdateConfig.Arg arg) {
        if (Objects.equals(AccountsReceivableConstants.MATCH_WAY_KEY, arg.getKey())) {
            if (MatchWayEnum.get((Integer) arg.getValue()).isPresent()) {
                arConfigManagerNew.updateMatchWay(serviceContext.getUser(), MatchWayEnum.get((Integer) arg.getValue()).get());
            }
        }
        return new AccountsReceivableType.UpdateConfig.Result();
    }

    @Override
    public AccountsReceivableType.UpdateArDetailPriceTaxAmount.Result updateArDetailPriceTaxAmount(AccountsReceivableType.UpdateArDetailPriceTaxAmount.Arg arg) {
        log.info("updateArDetailPriceTaxAmount  arg[{}]", arg);

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new AccountsReceivableType.UpdateArDetailPriceTaxAmount.Result();
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("updateArDetailPriceTaxAmount  tenantId[{}]", tenantId);

            IObjectDescribe objectDescribe = commonDescribeManagerNew.findByTenantIdAndDescribeApiName(tenantId, AccountsReceivableDetailObjConstants.API_NAME);

            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName);
            log.info("addFieldDescribes    fieldDescribe[{}]", fieldDescribe);

            boolean needReplace = false;
            if (fieldDescribe == null) {
                needReplace = true;
            } else {
                if (!Objects.equals("currency", fieldDescribe.getType())) {
                    needReplace = true;
                    objectDescribe.removeFieldDescribe(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName);
                }
            }
            log.info("updateArDetailPriceTaxAmount  tenantId[{}], needReplace[{}]", tenantId, needReplace);

            if (needReplace) {
                CurrencyFieldDescribe priceTaxAmount = CurrencyFieldDescribeBuilder.builder()
                        .apiName(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName).label(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.label).defaultIsExpression(true)
                        .required(true).defaultValue("$ar_quantity$*$tax_price$").maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build();
                objectDescribe.addFieldDescribe(priceTaxAmount);

                commonDescribeManagerNew.replace(objectDescribe);
            }
        }

        return new AccountsReceivableType.UpdateArDetailPriceTaxAmount.Result();
    }

    @Override
    public AccountsReceivableType.UpdateOrderProductIdWhere.Result updateOrderProductIdWhere(AccountsReceivableType.UpdateOrderProductIdWhere.Arg arg) {
        long begin = System.currentTimeMillis();
        log.info("updateOrderProductIdWhere begin arg[{}], begin[{}]", arg, begin);

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new AccountsReceivableType.UpdateOrderProductIdWhere.Result();
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("updateOrderProductIdWhere, begin[{}]  tenantId[{}]", begin, tenantId);

            //是否已开启
            AccountReceivableSwitchEnum switchStatus = arConfigManagerNew.getAccountReceivableStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountReceivableSwitchEnum.OPENED.getStatus())) {
                log.info("updateOrderProductIdWhere, begin[{}], tenantId[{}], switchStatus.getStatus[{}]", begin, tenantId, switchStatus.getStatus());
                continue;
            }

            IObjectDescribe objectDescribe = commonDescribeManagerNew.findByTenantIdAndDescribeApiName(tenantId, AccountsReceivableDetailObjConstants.API_NAME);
            IFieldDescribe orderProductId = objectDescribe.getFieldDescribe(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName);
            if (orderProductId == null) {
                log.info("fieldDescribe = null, begin[{}], tenantId[{}], objectDescribe[{}]", begin, tenantId, objectDescribe);
                continue;
            }
            objectDescribe.removeFieldDescribe(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName);

            orderProductId.set("wheres", ArFieldWheresDescribeUtil.getOrderProductIdWheres());

            objectDescribe.addFieldDescribe(orderProductId);

            commonDescribeManagerNew.replace(objectDescribe);
        }

        log.info("updateOrderProductIdWhere, begin[{}], arg[{}]", begin, arg);
        return new AccountsReceivableType.UpdateOrderProductIdWhere.Result();
    }

    private void sendModuleEnableMq(String tenantId) {
        try {
            AccountsReceivableEnableEvent event = new AccountsReceivableEnableEvent();
            event.setTenantId(tenantId);
            event.setAccountsReceivableEnable(true);

            Message message = new Message();
            message.setTopic("customer_account");
            message.setTags("accounts_receivable_enable");
            message.setBody(JSON.toJSONBytes(event));
            rocketMQMessageSender.send(message);
        } catch (Exception e) {
            log.warn("sendModuleEnableMq tenantId:{}", tenantId, e);
        }
    }

    @Override
    public AccountsReceivableType.AutoMatchReceivable.Result autoMatchReceivable(ServiceContext serviceContext, AccountsReceivableType.AutoMatchReceivable.Arg arg) {
        AccountsReceivableType.AutoMatchReceivable.Result result
                = AccountsReceivableType.AutoMatchReceivable.Result.builder().build();
        if (Strings.isNullOrEmpty(arg.getAccountId())) {
            throw new ValidateException(I18N.text("customeraccountobj.common.validate.params_error"));
        }

        String accountId = arg.getAccountId();
        if (Strings.isNullOrEmpty(accountId)) {
            return result;
        }

        List<IObjectData> arDataList = arCommonManagerNew.autoMatchReceivable(serviceContext.getUser()
                , arg.getAccountId(), arg.getAmount());
        result.setArDatas(ObjectDataDocument.ofList(arDataList));
        return result;
    }

    @Override
    public AccountsReceivableType.BulkCreateMatchNote.Result bulkCreateMatchNote(ServiceContext serviceContext, AccountsReceivableType.BulkCreateMatchNote.Arg arg) {
        AccountsReceivableType.BulkCreateMatchNote.Result result
                = AccountsReceivableType.BulkCreateMatchNote.Result.builder().build();
        if (!CollectionUtils.isNotEmpty(arg.getCheck_match_datas())) {
            throw new ValidateException(I18N.text("customeraccountobj.common.validate.params_error"));
        }

        String matchDataDescribeApiName = AccountsReceivableNoteObjConstants.API_NAME;
        String matchDataDetailDescribeApiName = AccountsReceivableDetailObjConstants.API_NAME;
        String paymentId = arg.getPaymentId();
        String debitDescribeApiName = Utils.CUSTOMER_PAYMENT_API_NAME;
        if (Strings.isNullOrEmpty(arg.getMatchDataDescribeApiName())) {
            matchDataDescribeApiName = AccountsReceivableNoteObjConstants.API_NAME;
        }
        if (Strings.isNullOrEmpty(arg.getMatchDataDetailDescribeApiName())) {
            matchDataDetailDescribeApiName = AccountsReceivableDetailObjConstants.API_NAME;
        }
        if (!Objects.isNull(arg.getDebitMatchData())) {
            paymentId = arg.getDebitMatchData().getDataId();
            debitDescribeApiName = arg.getDebitMatchData().getDescribeApiName();
        }
        DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(serviceContext.getTenantId()
                , matchDataDescribeApiName, DmDefineConstants.MATCH_NOTE);
        arCommonManagerNew.bulkCreateMatchNote(serviceContext.getUser(), paymentId, debitDescribeApiName, arg.getCheck_match_datas()
                , matchDataDescribeApiName, matchDataDetailDescribeApiName, DomainPluginDescribeExt.of(matchDataDescribeApiName, pluginParam), serviceContext.getEventId());
        return result;
    }
    @Override
    public AccountsReceivableType.CheckPaymentMatchNote.Result checkPaymentMatchNote(ServiceContext serviceContext, AccountsReceivableType.CheckPaymentMatchNote.Arg arg) {
        boolean checked = commonAccountsReceivableNoteManagerNew.checkPaymentMatchNote(serviceContext.getUser(), arg.getPayment().toObjectData());
        AccountsReceivableType.CheckPaymentMatchNote.Result result
                = AccountsReceivableType.CheckPaymentMatchNote.Result.builder()
                .result(checked).build();

        return result;
    }

    @Override
    public AccountsReceivableType.AutoMatchNote.Result autoMatchNote(ServiceContext serviceContext, AccountsReceivableType.AutoMatchNote.Arg arg) {
        autoMatchNoteTaskService.createOrUpdateTask(serviceContext.getTenantId(), arg.getObjectDescribeApiName(), arg.getObjectDataId());

        return AccountsReceivableType.AutoMatchNote.Result.builder().result(true).build();
    }

    @Override
    public AccountsReceivableType.PeriodicAccountsReceivable.Result periodicAccountsReceivable(ServiceContext serviceContext, AccountsReceivableType.PeriodicAccountsReceivable.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDetailDataId()) || Strings.isNullOrEmpty(arg.getMasterDescribeApiName())|| Strings.isNullOrEmpty(arg.getDetailDescribeApiName())) {
            throw new ValidateException(I18N.text("customeraccountobj.common.validate.params_error"));
        }
        DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(serviceContext.getTenantId()
                , arg.getMasterDescribeApiName(), DmDefineConstants.PERIOD_PRODUCT);
        periodicAccountsReceivableTaskService.createOrUpdateTask(serviceContext.getTenantId()
                , arg.getMasterDescribeApiName(), arg.getDetailDescribeApiName(), arg.getDetailDataId(), DomainPluginDescribeExt.of(arg.getMasterDescribeApiName(), pluginParam), null);
        return AccountsReceivableType.PeriodicAccountsReceivable.Result.builder().result(true).build();
    }

    @Override
    public AccountsReceivableType.CommonResult createOrUpdateSettlementDetailRule(ServiceContext serviceContext, CreateRule.Arg arg) {
        multiSourceCommonService.createOrUpdateRule(serviceContext, arg, SFAPreDefineObject.SettlementDetail.getApiName(), "related_settlement_detail_count");
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }

    @Override
    public AccountsReceivableType.CommonResult deleteSettlementDetailRule(ServiceContext serviceContext, DeleteRule.Arg arg) {
        multiSourceCommonService.deleteOrderPaymentRule(serviceContext, arg, SFAPreDefineObject.SettlementDetail.getApiName(), "related_settlement_detail_count");
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }

    @Override
    public AccountsReceivableType.GetSettlementRulesPreData.Result getSettlementRulesPreData(ServiceContext serviceContext, AccountsReceivableType.GetSettlementRulesPreData.Arg arg) {
        return commonAccountsReceivableNoteManagerNew.getSettlementRulesPreData(serviceContext.getUser(), arg.getObjectDataId());
    }

    @Override
    public AccountsReceivableType.CommonResult modifyPeriodicAccountsReceivable(ServiceContext serviceContext, AccountsReceivableType.ModifyPeriodicAccountsReceivable.Arg arg) {
        commonAccountsReceivableNoteManagerNew.modifyPeriodicAccountsReceivable(serviceContext.getUser(), arg.getDetailData().toObjectData());
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }

    @Override
    public AccountsReceivableType.CommonResult arQuickRuleCreateOrUpdate(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.CreateOrUpdateArg arg){
        User user = serviceContext.getUser();
        if (Strings.isNullOrEmpty(arg.getId())) {//新建
            IObjectData data = BuildArQuickRuleData(user, arg);
/*
            List<Wheres> wheresList = new ArrayList<>();
            parseConditions(arg, wheresList);
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setWheres(wheresList);
            query.setPermissionType(0);
            query.setNeedReturnCountNum(false);
            query.setNeedReturnQuote(false);
            query.setLimit(2000);
            List<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(serviceContext.getUser(), SFAPreDefineObject.SalesOrder.getApiName(), query).getData();
*/

            serviceFacade.saveObjectData(serviceContext.getUser(), data);
        } else {//编辑
            checkCanDeleteArQuickRule(serviceContext.getUser(), arg.getId());

            IObjectData data = serviceFacade.findObjectData(user, arg.getId(), SFAPreDefineObject.AccountsReceivableQuickRule.getApiName());
            if (!Objects.isNull(data)) {
                data.setOwner(Lists.newArrayList(user.getUserId()));
                data.setName(arg.getName());
                data.set(AccountsReceivableQuickRuleConstants.OBJECT_API_NAME, arg.getObjectApiName());
                data.set(AccountsReceivableQuickRuleConstants.CONDITIONS, arg.getConditions());
                data.set(AccountsReceivableQuickRuleConstants.MAX_PERIOD_COUNT, arg.getMaxPeriodCount());
                data.set(AccountsReceivableQuickRuleConstants.MINIMUM_AMOUNT_CONDITION, arg.getMinimumAmountCondition());
                data.set(AccountsReceivableQuickRuleConstants.INFORMATION_FIELDS, arg.getInformationFields());
                serviceFacade.updateObjectData(user, data);
            }
        }
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }

    private static void parseConditions(AccountsReceivableType.ArQuickRuleModel.CreateOrUpdateArg arg, List<Wheres> wheresList) {
        JSONArray jsonArray = JSONObject.parseArray(arg.getConditions());
        for (Object jsonObject : jsonArray) {
            List<IFilter> filters = Lists.newArrayList();
            filters.addAll(JSON.parseArray(((JSONObject) jsonObject).getString("filters"), IFilter.class));
            Wheres orWheres1 = new Wheres();
            orWheres1.setConnector(Where.CONN.OR.toString());
            orWheres1.setFilters(Lists.newArrayList(filters));
            wheresList.add(orWheres1);
        }
    }

    private IObjectData BuildArQuickRuleData(User user, AccountsReceivableType.ArQuickRuleModel.CreateOrUpdateArg arg) {
        IObjectData baseData = new ObjectData();
        baseData.setTenantId(user.getTenantId());
        baseData.setDescribeApiName(SFAPreDefineObject.AccountsReceivableQuickRule.getApiName());
        baseData.setOwner(Lists.newArrayList(user.getUserId()));
        baseData.setName(arg.getName());
        baseData.set(AccountsReceivableQuickRuleConstants.OBJECT_API_NAME, arg.getObjectApiName());
        baseData.set(AccountsReceivableQuickRuleConstants.CONDITIONS, arg.getConditions());
        baseData.set(AccountsReceivableQuickRuleConstants.MAX_PERIOD_COUNT, arg.getMaxPeriodCount());
        baseData.set(AccountsReceivableQuickRuleConstants.MINIMUM_AMOUNT_CONDITION, arg.getMinimumAmountCondition());
        baseData.set(AccountsReceivableQuickRuleConstants.INFORMATION_FIELDS, arg.getInformationFields());

        return baseData;
    }

    @Override
    public AccountsReceivableType.ArQuickRuleModel.ListResult arQuickRuleList(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.ListArg arg){
        List<IObjectData> queryResult = accountsReceivableQuickRuleDao.getDataListByApiName(serviceContext.getUser(), arg.getObjectApiName());
        return AccountsReceivableType.ArQuickRuleModel.ListResult.builder().ruleDatas(ObjectDataDocument.ofList(queryResult)).build();
    }

    @Override
    public AccountsReceivableType.CommonResult arQuickRuleDelete(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.DeleteArg arg){
        checkCanDeleteArQuickRule(serviceContext.getUser(), arg.getId());
        IObjectData data = serviceFacade.findObjectData(serviceContext.getUser(), arg.getId(), SFAPreDefineObject.AccountsReceivableQuickRule.getApiName());
        if (!Objects.isNull(data)) {
            serviceFacade.bulkDeleteWithInternalDescribe(Lists.newArrayList(data), serviceContext.getUser());
        }
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }

    @Override
    public AccountsReceivableType.ArQuickRuleModel.CheckArQuickResult checkArQuick(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.CheckArQuickArg arg) {
        String objectApiName = arg.getDescribeApiName();
        String objectId = arg.getObjectId();
        IObjectData data = serviceFacade.findObjectData(serviceContext.getUser(), objectId, objectApiName);
        if (Objects.isNull(data)) {
            throw new ValidateException(I18N.text("sfa.data.is.not.find"));
        }
        List<IObjectData> objectArRuleRelatedList = objectArRuleRelatedDao.getDataListByObjectIdApiName(serviceContext.getUser(), objectApiName, objectId);
        if (CollectionUtils.isEmpty(objectArRuleRelatedList)) {

        } else {

        }
        return AccountsReceivableType.ArQuickRuleModel.CheckArQuickResult.builder().build();
    }

    @Override
    public void test(ServiceContext serviceContext) {
        IObjectData data = serviceFacade.findObjectData(serviceContext.getUser(), "67cab0aa1426030007dc243b", AccountsReceivableNoteObjConstants.API_NAME);
        List<Map<String, Object>> attachmentList = data.get("attachment", List.class);
        fdfAttachment.parseText(serviceContext.getTenantId(), attachmentList);
    }

    private void checkCanDeleteArQuickRule(User user, String ruleId) {
        List<IObjectData> result = objectArRuleRelatedDao.getUnCompleteDataListByRuleId(user, ruleId);
        if (CollectionUtils.isNotEmpty(result)) {
            throw new ValidateException(I18N.text("sfa.arquickrule.isused.cannont.delete", result.get(0).getName()));
        }
    }

    @Override
    public AccountsReceivableType.CommonResult kxOpenSwitchKey(ServiceContext serviceContext) {
        if (!bizConfigThreadLocalCacheService.isOpenKxAutoMatch(serviceContext.getTenantId())) {
            return AccountsReceivableType.CommonResult.builder().isSuccess(false).build();
        }
        List<String> describeList = Lists.newArrayList(Utils.CUSTOMER_PAYMENT_API_NAME, Utils.ORDER_PAYMENT_API_NAME);
        Map<String, IObjectDescribe> describeMap = describeWithSimplifiedChineseService.findByDescribeApiNameList(serviceContext.getUser(), describeList);
        //2、回款加字段
        arPaymentManagerNew.addFieldForOpenAccountsReceivable(serviceContext.getUser(),describeMap);
        return AccountsReceivableType.CommonResult.builder().isSuccess(true).build();
    }
}