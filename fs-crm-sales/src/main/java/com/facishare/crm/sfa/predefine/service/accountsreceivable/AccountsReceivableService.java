
package com.facishare.crm.sfa.predefine.service.accountsreceivable;

import com.facishare.crm.sfa.predefine.service.accountsreceivable.dto.AccountsReceivableType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.DeleteRule;


@ServiceModule("accounts_receivable")
public interface AccountsReceivableService {

    /**
     * 启用应收
     */
    @ServiceMethod("enable_accounts_receivable")
    AccountsReceivableType.EnableAccountsReceivableResult enableAccountsReceivable(ServiceContext serviceContext);

    /**
     * 刷数据，addRuntimeConfig
     */
    @ServiceMethod("add_runtime_config")
    AccountsReceivableType.AddRuntimeConfig.Result addRuntimeConfig(ServiceContext serviceContext, AccountsReceivableType.AddRuntimeConfig.Arg arg);

    /**
     * 查询"应收开关"是否开启
     */
    @ServiceMethod("get_config")
    AccountsReceivableType.GetConfig.Result getConfig(ServiceContext serviceContext, AccountsReceivableType.GetConfig.Arg arg);

    /**
     * 更新配置
     */
    @ServiceMethod("update_config")
    AccountsReceivableType.UpdateConfig.Result updateConfig(ServiceContext serviceContext, AccountsReceivableType.UpdateConfig.Arg arg);

    /**
     * 更新 回款明细 price_tax_amount
     */
    @ServiceMethod("update_ardetail_price_tax_amount")
    AccountsReceivableType.UpdateArDetailPriceTaxAmount.Result updateArDetailPriceTaxAmount(AccountsReceivableType.UpdateArDetailPriceTaxAmount.Arg arg);

    /**
     * 更新 AccountsReceivableDetailObj.orderProductId的wheres
     */
    @ServiceMethod("update_order_product_id_where")
    AccountsReceivableType.UpdateOrderProductIdWhere.Result updateOrderProductIdWhere(AccountsReceivableType.UpdateOrderProductIdWhere.Arg arg);

    /**
     * 核销自动匹配应收
     */
    @ServiceMethod("auto_match_receivable")
    AccountsReceivableType.AutoMatchReceivable.Result autoMatchReceivable(ServiceContext serviceContext, AccountsReceivableType.AutoMatchReceivable.Arg arg);

    /**
     * 通过应收生成核销
     */
    @ServiceMethod("bulk_create_match_note")
    AccountsReceivableType.BulkCreateMatchNote.Result bulkCreateMatchNote(ServiceContext serviceContext, AccountsReceivableType.BulkCreateMatchNote.Arg arg);

    @ServiceMethod("check_payment_match_note")
    AccountsReceivableType.CheckPaymentMatchNote.Result checkPaymentMatchNote(ServiceContext serviceContext, AccountsReceivableType.CheckPaymentMatchNote.Arg arg);

    /**
     * 自动匹配核销
     * 功能:通过（应收单，蓝字回款）等对象触发自动核销匹配。
     * 注意事项：该函数目前只支持开启自动核销场景下才可以使用
     */
    @ServiceMethod("auto_match_note")
    AccountsReceivableType.AutoMatchNote.Result autoMatchNote(ServiceContext serviceContext, AccountsReceivableType.AutoMatchNote.Arg arg);

    @ServiceMethod("periodic_accounts_receivable")
    AccountsReceivableType.PeriodicAccountsReceivable.Result periodicAccountsReceivable(ServiceContext serviceContext, AccountsReceivableType.PeriodicAccountsReceivable.Arg arg);

    @ServiceMethod("create_or_update_settlementdetail_rule")
    AccountsReceivableType.CommonResult createOrUpdateSettlementDetailRule(ServiceContext serviceContext, CreateRule.Arg arg);

    @ServiceMethod("delete_settlementdetail_rule")
    AccountsReceivableType.CommonResult deleteSettlementDetailRule(ServiceContext serviceContext, DeleteRule.Arg arg);
    @ServiceMethod("get_settlement_rules_predata")
    AccountsReceivableType.GetSettlementRulesPreData.Result getSettlementRulesPreData(ServiceContext serviceContext, AccountsReceivableType.GetSettlementRulesPreData.Arg arg);
    @ServiceMethod("modify_eriodic_accountsreceivable")
    AccountsReceivableType.CommonResult modifyPeriodicAccountsReceivable(ServiceContext serviceContext, AccountsReceivableType.ModifyPeriodicAccountsReceivable.Arg arg);

    @ServiceMethod("ar_quick_rule_create_or_update")
    AccountsReceivableType.CommonResult arQuickRuleCreateOrUpdate(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.CreateOrUpdateArg arg);

    @ServiceMethod("ar_quick_rule_list")
    AccountsReceivableType.ArQuickRuleModel.ListResult arQuickRuleList(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.ListArg arg);

    @ServiceMethod("ar_quick_rule_delete")
    AccountsReceivableType.CommonResult arQuickRuleDelete(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.DeleteArg arg);

    @ServiceMethod("check_ar_quick")
    AccountsReceivableType.ArQuickRuleModel.CheckArQuickResult checkArQuick(ServiceContext serviceContext, AccountsReceivableType.ArQuickRuleModel.CheckArQuickArg arg);

    @ServiceMethod("kx_open_switch_key")
    AccountsReceivableType.CommonResult kxOpenSwitchKey(ServiceContext serviceContext);

    @ServiceMethod("test")
    void test(ServiceContext serviceContext);
}