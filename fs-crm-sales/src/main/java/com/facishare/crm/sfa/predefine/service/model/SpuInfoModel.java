package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/7/22 15:07
 */
public interface SpuInfoModel {
    @Data
    class Arg {
        private List<String> spuIdList;
    }

    @Data
    @Builder
    class Result {
        private String errorCode;
        private String message;
        private List<ObjectDataDocument> spuInfoList;
    }

    @Data
    @Builder
    class SpuInfo {
        private String id;
        private String name;
        private String category;
        private String categoryName;
        private List<Map> picture;
        @JSONField(name = "isSpec")
        @SerializedName("isSpec")
        private Boolean isSpec;
        private List<ObjectDataDocument> skuList;
    }
}
