package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.CommonProductConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.cache.RedisDataAccessor;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomPriceService;
import com.facishare.crm.sfa.predefine.service.cpq.BomService;
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryBomPriceModel;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants.SalesOrderProductField;
import com.facishare.crm.sfa.utilities.constant.dmConstants.BomDmConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.CalculateService;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.BatchCalculate;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.MoreObjects;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.DBRecord.ID;

@Service
@Slf4j
public class BomPriceServiceImpl implements BomPriceService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    AttributeCoreService attributeCoreService;
    @Autowired
    AvailableRangeCoreService availableRangeCoreService;
    @Autowired
    ObjectDescribeServiceImpl objectDescribeService;
    @Autowired
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    CalculateService calculateService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private BomService bomService;
    @Autowired
    private RedisDataAccessor redisDataAccessor;

    private static final String ADJUST_PRICE = "adjust_price";
    private static final String MODIFY_PRICE = "modify_price";
    private static final String NODE_PRICE = "node_price";
    private static final String NODE_DISCOUNT = "node_discount";
    private static final String NODE_SUBTOTAL = "node_subtotal";
    private static final String MODIFY_AMOUNT = "modify_amount";
    private static final String MODIFY_PRICING_PERIOD = "modify_pricing_period";
    private static final String DEFAULT_PRICE_PER_SET = "default_price_per_set";
    private static final String BALANCE_PRICE_PER_SET = "balance_price_per_set";
    private static final String SALES_PRICE = "sales_price";
    private static final String SHARE_RATE = "share_rate";
    private static final String AMOUNT = "amount";
    private static final String PRICING_PERIOD = "pricing_period";
    private static final String PRICE_PER_SET = "price_per_set";
    private static final String NODE_BALANCE = "node_balance";
    private static final String USER_SELECTED_NODE = "user_selected_node";
    private static final String NON_USER_SELECTED_NODE = "non_user_selected_node";
    private static final String PRICE_BOOK_DISCOUNT = "price_book_discount";
    private static final String PRODUCT_ID_R = "product_id__r";

    @Override
    public List<ObjectDataDocument> queryBomPrice(ServiceContext context, QueryBomPriceModel.Param entity) {
        StopWatch stopWatch = StopWatch.createUnStarted("BomPriceServiceImpl#queryBomPrice");
        List<IObjectData> resultList;
        List<IObjectData> allList;
        boolean isBomMasterSlaveMode = GrayUtil.bomMasterSlaveMode(context.getTenantId());
        try {
            verifyParam(entity);
            boolean bomPriceConfig = bizConfigThreadLocalCacheService.bomPriceCalculationConfiguration(context.getTenantId());
            if (!entity.isNoCalPrice()) {
                //此时tmpDataList 不包含 复用Bom下的数据
                List<IObjectData> tmpDataList = findBySearchQueryIgnoreAll(context.getUser(), Utils.BOM_API_NAME, query(entity.getRootBomId()));
                stopWatch.lap("loadData");
                if (CollectionUtils.isEmpty(tmpDataList)) {
                    return Collections.emptyList();
                }
                if (isBomMasterSlaveMode) {
                    //填充复用BOM的数据
                    stopWatch.lap("fillReusedBomList start");
                    fillReusedBomList(context, entity, tmpDataList);
                    stopWatch.lap("fillReusedBomList end");
                }
                Map<String, IObjectData> dataMap = Maps.newHashMap();
                Map<String, IObjectData> path2DataMap = Maps.newHashMap();
                Multimap<String, IObjectData> childMap = ArrayListMultimap.create();
                Multimap<String, IObjectData> dataMultimap = ArrayListMultimap.create();
                List<IObjectData> dataList = new ArrayList<>();
                String coreId = "";
                for (IObjectData x : tmpDataList) {
                    String tmpId = x.get(BomConstants.FIELD_CORE_ID, String.class);
                    if (StringUtils.isNotBlank(tmpId)) {
                        coreId=tmpId;
                    }
                    if (x.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, false)) {
                        if (StringUtils.isBlank(x.get(BomConstants.FIELD_BOM_PATH, String.class))) {
                            log.warn("bomPath is blank id:{}", x.getId());
                            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_FIELD_PATH_NOT_NULL));
                        }
                        if (!Objects.equals(x.getId(), entity.getRootBomId())) {
                            x.set(BomConstants.FIELD_NODE_TYPE, EnumUtil.nodeType.stand.getValue());
                        }
                        dataList.add(x);
                        dataMap.put(x.getId(), x);
                        path2DataMap.put(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class), x);
                        if (x.get("selected_by_default", Boolean.class, false) && StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))) {
                            childMap.put(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class), x);
                        }
                        dataMultimap.put(x.getId(), x);
                    }
                }
                if (CollectionUtils.isEmpty(dataList)) {
                    return Collections.emptyList();
                }

                IObjectDescribe bomDescribe = serviceFacade.findObject(context.getTenantId(), Utils.BOM_API_NAME);
                allList = Lists.newArrayList();

                //处理临时节点
                if (isBomMasterSlaveMode) {
                    handleTempNodeByNewBomPath(entity, dataList, bomDescribe);
                    //包括填充 PROD_PKG，ROOT_PROD_PKG,allList在方法内赋值
                    resultList = choiceDataListByNewBomPath(entity, dataList, path2DataMap);
                    log.info("choiceDataListByNewBomPath：{}", JSON.toJSONString(resultList));
                } else {
                    Map<String, QueryBomPriceModel.BomInfo> tmpMap = entity.getBomList().parallelStream().collect(Collectors.toMap(QueryBomPriceModel.BomInfo::getBomId, Function.identity(), (k1, k2) -> k1));
                    handleTempNode(entity, dataMap, dataList, bomDescribe);
                    //包括填充 PROD_PKG，ROOT_PROD_PKG,allList在方法内赋值
                    resultList = choiceDataList(entity, dataMap, tmpMap);
                }
                if (CollectionUtils.isEmpty(resultList)) {
                    return Collections.emptyList();
                }
                allList.addAll(resultList);
                handleSelectedData(resultList, childMap, dataMultimap, allList);

                RequestContext requestContext = RequestContextManager.getContext();
                //获取bom相关联对象信息
                CompletableFuture<Void> ctf = CompletableFuture.runAsync(() -> fillLookUpField(context, resultList, allList, requestContext, bomDescribe));
                stopWatch.lap("loadExtraData");
                entity.setPriority(bizConfigThreadLocalCacheService.isEnforcePriority(context.getTenantId()));
                //获取属性
                attributeCoreService.attachAttributeData(context.getUser(), resultList, BomConstants.FIELD_PRODUCT_ID);
                stopWatch.lap("attachAttributeData");
                //调用取价接口
                getBomPriceByCond(allList, entity, context.getUser());
                stopWatch.lap("getBomPriceByCond");
                ctf.join();
                List<IObjectData> bomList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), Lists.newArrayList(coreId), Utils.BOM_CORE_API_NAME);
                if (CollectionUtils.isNotEmpty(bomList)) {
                    IObjectData bomCoreData = bomList.get(0);
                    if (Objects.equals(bomCoreData.get(BomCoreConstants.SALE_STRATEGY, String.class), BomCoreConstants.SaleStrategy.sub.getValue())) {
                        setValue(entity, resultList);
                        return ObjectDataDocument.ofList(resultList);
                    }
                }
                if (entity.isSwitchMasterPriceBook()) {
                    switchPriceBookQueryBomPrice(entity, resultList, allList, bomPriceConfig, context.getTenantId());
                } else {
                    //计算子件的价格
                    if (isBomMasterSlaveMode) {
                        getChildNodePriceByNewBomPath(entity, resultList, path2DataMap, allList, bomPriceConfig,context.getTenantId());
                    } else {
                        getChildNodePrice(entity, resultList, dataMap, allList, bomPriceConfig);
                    }
                }
                //计算包的价格
                getPackagePrice(entity, resultList);
                stopWatch.lap("calculatePackagePrice");
            } else {
                resultList = Lists.newArrayList();
                allList = Lists.newArrayList();
            }
            //计算分摊
            calculateAllocation(entity, resultList, bomPriceConfig, context, allList);

            stopWatch.lap("calculateAllocation");
            String key = String.format("%s-%s-%s-%s-%s", context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId(), entity.getApiName(), entity.getAccountId(), entity.getRootBomId());
            redisDataAccessor.set(key, "" + resultList.size(), 30 * 60);
        } finally {
            stopWatch.logSlow(1000);
        }

        return ObjectDataDocument.ofList(resultList);
    }

    private void setValue(QueryBomPriceModel.Param entity, List<IObjectData> resultList) {
        HashMap<String, QueryBomPriceModel.BomInfo> bomMap = Maps.newHashMap();
        for (QueryBomPriceModel.BomInfo bomInfo : entity.getBomList()) {
            bomMap.put(bomInfo.getProdKey(),bomInfo);
        }
        resultList.forEach(x -> {
            QueryBomPriceModel.BomInfo bomInfo = bomMap.get(x.get(SalesOrderProductField.PROD_PKG.getApiName()));
            if (Objects.nonNull(bomInfo)) {
                x.set(ADJUST_PRICE, bomInfo.getPrice());
                x.set(AMOUNT, bomInfo.getAmount());
                x.set(PRICING_PERIOD, bomInfo.getPricingPeriod());
                x.set(PRICE_PER_SET, bomInfo.getPricePerSet());
            }
        });
    }

    private List<IObjectData> findBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery query) {
        List<IObjectData> result = Lists.newArrayList();
        // 当前循环次数
        int loop = 0;
        int queryDataSize;
        // 最多循环10次
        do {
            query.setOffset(loop++ * query.getLimit());
            List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
            if (CollectionUtils.isEmpty(dataList)) {
                queryDataSize = 0;
            } else {
                queryDataSize = dataList.size();
                result.addAll(dataList);
            }
        } while (queryDataSize >= query.getLimit() && loop < 10);
        return result;
    }

    private void verifyParam(QueryBomPriceModel.Param entity) {
        if (StringUtils.isAnyBlank(entity.getRootBomId(), entity.getAccountId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        if (CollectionUtils.isNotEmpty(entity.getBomList())) {
            entity.getBomList().forEach(x -> {
                if (StringUtils.equals(entity.getRootBomId(), x.getBomId())) {
                    if (StringUtils.isAnyBlank(x.getProdKey(), x.getRootProdKey())) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
                    }
                } else {
                    if (StringUtils.isAnyBlank(x.getProdKey(), x.getParentProdKey(), x.getRootProdKey())) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
                    }
                }
                if (StringUtils.isAnyBlank(x.getAmount())) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
                }
                if (StringUtils.isBlank(x.getBomId()) && !Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType())) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
                }
            });
        }
    }

    private void handleSelectedData(List<IObjectData> resultList, Multimap<String, IObjectData> childMap, Multimap<String, IObjectData> dataMultimap, List<IObjectData> allList) {
        resultList.forEach(x -> {
            Collection<IObjectData> childList = childMap.get(x.getId());
            if (CollectionUtils.isNotEmpty(childList)) {
                childList.forEach(d -> {
                    Collection<IObjectData> tmpList = dataMultimap.get(d.getId());
                    if (CollectionUtils.isNotEmpty(tmpList)) {
                        boolean flag = false;
                        IObjectData objectData = null;
                        for (IObjectData t : tmpList) {
                            if (Objects.equals(t.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class), x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class))) {
                                flag = true;
                                break;
                            }
                            if (StringUtils.isBlank(t.get(BomConstants.FIELD_PROD_PKG_KEY, String.class))) {
                                objectData = t;
                            }
                        }
                        if (!flag && Objects.nonNull(objectData)) {
                            objectData.set(SalesOrderProductField.PROD_PKG.getApiName(), serviceFacade.generateId());
                            objectData.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), x.get(BomConstants.FIELD_PROD_PKG_KEY));
                            objectData.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY));
                            objectData.set(NON_USER_SELECTED_NODE, true);
                            allList.add(objectData);
                        }
                    }
                });
            }
        });
    }

    /**
     * 填充复用BOM的数据
     *
     * @param context
     * @param entity           前端请求参数
     * @param toAppendDataList 待追加数据List
     */
    private void fillReusedBomList(ServiceContext context, QueryBomPriceModel.Param entity, List<IObjectData> toAppendDataList) {
        if (CollectionUtils.isEmpty(entity.getBomList()) || CollectionUtils.isEmpty(toAppendDataList)) {
            return;
        }
        Map<String, IObjectData> bomId2DataMap = Maps.newHashMap();
        //从数据库查询出来的数据，也需要给new_bom_path赋值处理
        toAppendDataList.forEach(x -> {

                    x.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, x.get(BomConstants.FIELD_BOM_PATH, String.class));
                    //设置当前节点属于哪个复用BOM的根节点
                    x.set(BomConstants.VIRTUAL_FIELD_CURRENT_ROOT_NEW_PATH, entity.getRootBomId());
                    bomId2DataMap.put(x.getId(), x);
                }
        );
        //启用了新BOM，但是参数中没有传core_id，则说明是历史数据，历史数据不存在复用bom,则无需其他处理，但是需要补充entity中的请求数据，补充new_bom_path信息，core_id
        if (entity.getCoreId() == null) {
            entity.getBomList().stream().forEach(bom -> {
                IObjectData data = bomId2DataMap.get(bom.getBomId());
                if (Objects.nonNull(data)) {
                    bom.setNewBomPath(data.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class));
                    bom.setCoreId(data.get(BomConstants.FIELD_CORE_ID, String.class));
                    bom.setRelatedCoreId(data.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
                    bom.setNodeBomCoreType(data.get(BomConstants.FIELD_NODE_BOM_CORE_TYPE, String.class));
                    bom.setNodeBomCoreVersion(data.get(BomConstants.FIELD_NODE_BOM_CORE_VERSION, String.class));
                }
            });
            entity.setCoreId(toAppendDataList.get(0).get(BomConstants.FIELD_CORE_ID, String.class));
            return;
        }
        //复用BOM的数据,用于还原new_bom_path
        Map<String, String> newBomPath2RelatedCoreId = entity.getBomList().stream().filter(x -> StringUtils.isNotBlank(x.getRelatedCoreId())).collect(Collectors.toMap(QueryBomPriceModel.BomInfo::getNewBomPath, QueryBomPriceModel.BomInfo::getRelatedCoreId, (k1, k2) -> k1));
        //复用BOM的数据,用于还原第一层的paren_bom_id,只关注复用bom节点的数据
        Map<String, String> newBomPath2BomId = entity.getBomList().stream().filter(x -> StringUtils.isNotBlank(x.getRelatedCoreId())).collect(Collectors.toMap(QueryBomPriceModel.BomInfo::getNewBomPath, QueryBomPriceModel.BomInfo::getBomId, (k1, k2) -> k1));
        if (MapUtils.isEmpty(newBomPath2RelatedCoreId)) {
            return;
        }

        //处理重复的复用bom，只能一个一个查
        newBomPath2RelatedCoreId.forEach((newBomPath, coreId) -> {
            List<IObjectData> subbomList = bomService.getBomListByCoreId(coreId, context.getUser(), false, false);
            if (CollectionUtils.isNotEmpty(subbomList)) {
                //处理查询出来的子件做root_id替换，new_bom_path替换
                subbomList.forEach(x -> {
                    //根节点时可能没传newBomPath
                    x.set(BomConstants.FIELD_ROOT_ID, entity.getRootBomId());
                    x.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, bomService.getNewBomPath(x, newBomPath));
                    String bomPath = x.get(BomConstants.FIELD_BOM_PATH, String.class);
                    //复用BOM子节点第一层，需要替换parent_bom_id
                    if (StringUtils.isNotBlank(bomPath) && bomPath.split("\\.").length == 2) {
                        x.set(BomConstants.FIELD_PARENT_BOM_ID, newBomPath2BomId.get(newBomPath));
                    }
                    //设置当前节点属于哪个复用BOM的根节点
                    x.set(BomConstants.VIRTUAL_FIELD_CURRENT_ROOT_NEW_PATH, newBomPath);
                });
                toAppendDataList.addAll(subbomList);
            }
        });
    }


    private void handleTempNodeByNewBomPath(QueryBomPriceModel.Param entity, List<IObjectData> dataList, IObjectDescribe bomDescribe) {
        List<QueryBomPriceModel.BomInfo> tempNodes = entity.getBomList().stream().filter(x -> Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType()) && StringUtils.isNotBlank(x.getNewBomPath())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempNodes)) {
            return;
        }

        Map<String, String> tmpMap = entity.getBomList().stream().filter(x -> !Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType()))
                .collect(Collectors.toMap(QueryBomPriceModel.BomInfo::getProdKey, QueryBomPriceModel.BomInfo::getBomId, (x1, x2) -> x1));

        for (QueryBomPriceModel.BomInfo tempBomInfo : tempNodes) {
            String parentKey = tempBomInfo.getParentProdKey();
            String parentBomId = tmpMap.get(parentKey);
            if (StringUtils.isBlank(parentKey) || StringUtils.isBlank(parentBomId)) {
                continue;
            }
            IObjectData addData = new ObjectData();
            addData.set(BomConstants.FIELD_NODE_TYPE, tempBomInfo.getNodeType());
            addData.setId(serviceFacade.generateId());
            addData.setDescribeApiName(Utils.BOM_API_NAME);
            addData.set(BomConstants.FIELD_PRODUCT_ID, tempBomInfo.getProductId());
            IFieldDescribe fieldDescribe = bomDescribe.getFieldDescribe(BomConstants.FIELD_PRICE_MODE);
            if (Objects.nonNull(fieldDescribe) && Objects.nonNull(fieldDescribe.getDefaultValue())) {
                addData.set(BomConstants.FIELD_PRICE_MODE, fieldDescribe.getDefaultValue());
            }
            IFieldDescribe amountAnyField = bomDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT_ANY);
            if (Objects.nonNull(amountAnyField) && Objects.nonNull(amountAnyField.getDefaultValue())) {
                addData.set(BomConstants.FIELD_AMOUNT_ANY, amountAnyField.getDefaultValue());
            }
            addData.set(BomConstants.FIELD_PARENT_BOM_ID, parentBomId);
            addData.set(BomConstants.FIELD_ROOT_ID, entity.getRootBomId());

            addData.set(BomConstants.FIELD_BOM_PATH, tempBomInfo.getNewBomPath());
            addData.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, tempBomInfo.getNewBomPath());

            addData.set(BomConstants.FIELD_AMOUNT, tempBomInfo.getAmount());
            addData.set(SalesOrderProductField.PROD_PKG.getApiName(), tempBomInfo.getProdKey());
            addData.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), tempBomInfo.getParentProdKey());
            addData.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), tempBomInfo.getRootProdKey());
            dataList.add(addData);
            tempBomInfo.setBomId(addData.getId());
        }
    }

    private void handleTempNode(QueryBomPriceModel.Param entity, Map<String, IObjectData> dataMap, List<IObjectData> dataList, IObjectDescribe bomDescribe) {
        List<QueryBomPriceModel.BomInfo> tempNodes = entity.getBomList().stream().filter(x -> Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempNodes)) {
            return;
        }
        Map<String, String> tmpMap = entity.getBomList().stream().filter(x -> !Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType()))
                .collect(Collectors.toMap(QueryBomPriceModel.BomInfo::getProdKey, QueryBomPriceModel.BomInfo::getBomId, (x1, x2) -> x1));
        for (QueryBomPriceModel.BomInfo x : tempNodes) {
            String parentKey = x.getParentProdKey();
            if (StringUtils.isBlank(parentKey)) {
                continue;
            }
            String parentBomId = tmpMap.get(parentKey);
            if (StringUtils.isBlank(parentBomId)) {
                continue;
            }
            IObjectData parentData = dataMap.get(parentBomId);
            if (Objects.isNull(parentData)) {
                continue;
            }
            IObjectData addData = new ObjectData();
            addData.set(BomConstants.FIELD_NODE_TYPE, x.getNodeType());
            addData.setId(serviceFacade.generateId());
            addData.setDescribeApiName(Utils.BOM_API_NAME);
            addData.set(BomConstants.FIELD_PRODUCT_ID, x.getProductId());
            IFieldDescribe fieldDescribe = bomDescribe.getFieldDescribe(BomConstants.FIELD_PRICE_MODE);
            if (Objects.nonNull(fieldDescribe) && Objects.nonNull(fieldDescribe.getDefaultValue())) {
                addData.set(BomConstants.FIELD_PRICE_MODE, fieldDescribe.getDefaultValue());
            }
            IFieldDescribe amountAnyField = bomDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT_ANY);
            if (Objects.nonNull(amountAnyField) && Objects.nonNull(amountAnyField.getDefaultValue())) {
                addData.set(BomConstants.FIELD_AMOUNT_ANY, amountAnyField.getDefaultValue());
            }
            addData.set(BomConstants.FIELD_PARENT_BOM_ID, parentData.getId());
            addData.set(BomConstants.FIELD_ROOT_ID, parentData.get(BomConstants.FIELD_ROOT_ID));
            addData.set(BomConstants.FIELD_BOM_PATH, parentData.get(BomConstants.FIELD_BOM_PATH, String.class).concat(".").concat(addData.getId()));
            addData.set(BomConstants.FIELD_AMOUNT, "1");
            addData.setTenantId(parentData.getTenantId());
            addData.set(SalesOrderProductField.PROD_PKG.getApiName(), x.getProdKey());
            addData.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), x.getParentProdKey());
            addData.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), x.getRootProdKey());
            dataList.add(addData);
            dataMap.put(addData.getId(), addData);
            x.setBomId(addData.getId());
        }
    }

    /**
     * 计算分摊
     *
     * @param entity
     * @param resultList
     * @param bomPriceConfig
     * @param allList
     */
    private void calculateAllocation(QueryBomPriceModel.Param entity, List<IObjectData> resultList, boolean bomPriceConfig, ServiceContext serviceContext, List<IObjectData> allList) {
        IObjectDescribe objectDescribe = null;
        IFieldDescribe productPriceFieldDescribe = null;
        IFieldDescribe nodeDiscountFieldDescribe = null;
        DomainPluginParam.DetailObj detailObj = null;
        if (StringUtils.isNotBlank(entity.getApiName())) {
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(serviceContext.getTenantId(), entity.getApiName(), DmDefineConstants.BOM);
            if (Objects.nonNull(pluginParam) && CollectionUtils.isNotEmpty(pluginParam.getDetails())) {
                if (pluginParam.getDetails().size() == 1) {
                    detailObj = pluginParam.getDetails().get(0);
                } else {
                    for (DomainPluginParam.DetailObj detail : pluginParam.getDetails()) {
                        if (Objects.equals(detail.getObjectApiName(), entity.getDetailApiName())) {
                            detailObj = detail;
                        }
                    }
                }
            }
        }
        if (Objects.nonNull(detailObj)) {
            objectDescribe = serviceFacade.findObject(serviceContext.getTenantId(), detailObj.getObjectApiName());
            if (Objects.nonNull(objectDescribe)) {
                productPriceFieldDescribe = objectDescribe.getFieldDescribe(detailObj.getFieldMapping().getOrDefault(BomDmConstants.DetailField.PRODUCT_PRICE, "price"));
                nodeDiscountFieldDescribe = objectDescribe.getFieldDescribe(detailObj.getFieldMapping().getOrDefault(NODE_DISCOUNT, NODE_DISCOUNT));
            }
        }

        int nodeDiscountScale = Objects.isNull(nodeDiscountFieldDescribe) ? 2 : nodeDiscountFieldDescribe.get("decimal_places", Integer.class, 2);
        int productPriceScale = Objects.isNull(productPriceFieldDescribe) ? 2 : productPriceFieldDescribe.get("decimal_places", Integer.class, 2);
        if (!entity.isNoCalPrice()) {
            calculateNodePriceAndDiscount(resultList, bomPriceConfig, nodeDiscountScale, productPriceScale, allList);
        } else {
            for (QueryBomPriceModel.BomInfo x : entity.getBomList()) {
                IObjectData data = new ObjectData();
                data.set(ID, x.getBomId());
                data.set(AMOUNT, x.getAmount());
                if (Objects.equals(x.getBomId(), entity.getRootBomId())) {
                    data.set(PRICE_BOOK_DISCOUNT, entity.getPriceBookDiscount());
                }
                if (StringUtils.equals(x.getParentProdKey(), x.getRootProdKey())) {
                    data.set(BomConstants.FIELD_PARENT_BOM_ID, entity.getRootBomId());
                }
                data.set(BomConstants.FIELD_PARENT_BOM_ID, x.getParentBomId());
                data.set(NODE_PRICE, Objects.isNull(x.getOriginPrice()) ? 0 : x.getOriginPrice());
                data.set(ADJUST_PRICE, x.getPrice());
                data.set(PRICING_PERIOD, x.getPricingPeriod());
                data.set(PRICE_PER_SET, x.getPricePerSet());
                if (data.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                    data.set(NODE_DISCOUNT, 0);
                } else {
                    //选配折扣
                    data.set(NODE_DISCOUNT, data.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(data.get(NODE_PRICE, BigDecimal.class), nodeDiscountScale, BigDecimal.ROUND_HALF_UP));
                }
                data.set(BomConstants.FIELD_PRODUCT_GROUP_ID, x.getProduct_group_id());
                data.set(BomConstants.FIELD_PRODUCT_GROUP_ID.concat("__r"), x.getProduct_group_id__r());
                data.set(SHARE_RATE, x.getShareRate());
                data.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, x.getNewBomPath());
                data.set(BomConstants.FIELD_PROD_PKG_KEY, x.getProdKey());
                data.set(BomConstants.FIELD_PARENT_PROD_PKG_KEY, x.getParentProdKey());
                data.set(BomConstants.FIELD_ROOT_PROD_PKG_KEY, x.getRootProdKey());
                data.set(BomConstants.FIELD_NODE_TYPE, x.getNodeType());
                resultList.add(data);
            }
        }
        if (!(allocate(entity, resultList) && StringUtils.equalsAny(entity.getApiName(), Utils.SALES_ORDER_API_NAME))) {
            return;
        }
        if (Objects.isNull(objectDescribe)) {
            return;
        }
        BigDecimal balance = calculateBalance(entity, resultList, serviceContext);
        DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(serviceContext.getTenantId(), entity.getApiName(), DmDefineConstants.PERIOD_PRODUCT);
        calculateNodeAllocationField(entity, resultList, balance, objectDescribe, Objects.nonNull(pluginParam));
    }

    //是否配置分摊
    private boolean allocate(QueryBomPriceModel.Param entity, List<IObjectData> resultList) {
        //根节点下的一级子节点
        List<IObjectData> oneLevelNodes = resultList.parallelStream().filter(x -> Objects.equals(entity.getRootBomId(), x.get(BomConstants.FIELD_PARENT_BOM_ID))
                && StringUtils.isNotBlank(x.get(SHARE_RATE, String.class))).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(oneLevelNodes);
    }

    /**
     * 计算分摊字段
     *
     * @param entity
     * @param resultList
     * @param balance
     * @param orderProductObjDesc
     */
    private void calculateNodeAllocationField(QueryBomPriceModel.Param entity, List<IObjectData> resultList, BigDecimal balance, IObjectDescribe orderProductObjDesc, boolean periodPlugin) {
        BigDecimal rootDiscount = resultList.stream().filter(x -> Objects.equals(x.getId(), entity.getRootBomId())).map(x -> x.get(PRICE_BOOK_DISCOUNT, BigDecimal.class, new BigDecimal("100"))).findFirst().orElse(new BigDecimal("100"));
        BigDecimal rootAmount = StringUtils.isBlank(entity.getRootAmount()) ? BigDecimal.ONE : new BigDecimal(entity.getRootAmount());
        IFieldDescribe salePriceFieldDescribe = orderProductObjDesc.getFieldDescribe(SALES_PRICE);
        int salePriceScale = Objects.isNull(salePriceFieldDescribe) ? 2 : salePriceFieldDescribe.get("decimal_places", Integer.class, 2);
        IFieldDescribe discountFieldDescribe = orderProductObjDesc.getFieldDescribe("discount");
        int discountScale = Objects.isNull(discountFieldDescribe) ? 2 : discountFieldDescribe.get("decimal_places", Integer.class, 2);
        IFieldDescribe nodeSubtotalFieldDescribe = orderProductObjDesc.getFieldDescribe(NODE_SUBTOTAL);
        int nodeSubtotalScale = Objects.isNull(nodeSubtotalFieldDescribe) ? 2 : nodeSubtotalFieldDescribe.get("decimal_places", Integer.class, 2);
        resultList.stream().filter(x -> Objects.equals(x.get(BomConstants.FIELD_PARENT_BOM_ID), entity.getRootBomId())).forEach(x -> calculateNodeField(resultList, x, rootDiscount, balance, rootAmount, salePriceScale, discountScale, nodeSubtotalScale, periodPlugin));
    }


    /**
     * 计算差额
     *
     * @param entity
     * @param resultList
     */
    private BigDecimal calculateBalance(QueryBomPriceModel.Param entity, List<IObjectData> resultList, ServiceContext serviceContext) {
        IObjectData rootBom = resultList.parallelStream().filter(x -> Objects.equals(entity.getRootBomId(), x.getId())).findFirst().orElse(null);
        if (Objects.isNull(rootBom)) {
            return BigDecimal.ZERO;
        }
        BigDecimal subtotal = StringUtils.isBlank(entity.getRootSubtotal()) ? BigDecimal.ZERO : new BigDecimal(entity.getRootSubtotal());
        BatchCalculate.Arg calculateArg = entity.getCalculateArg();
        if (Objects.nonNull(calculateArg)) {
            Map<String, Map<String, ObjectDataDocument>> dataMap = calculateArg.getDetailDataMap();
            Map<String, ObjectDataDocument> detailDataMap = dataMap.get(Utils.SALES_ORDER_PRODUCT_API_NAME);
            if (StringUtils.isNotBlank(entity.getRootRowId())) {
                ObjectDataDocument dataDocument = detailDataMap.get(entity.getRootRowId());
                if (Objects.nonNull(dataDocument)) {
                    if (Objects.equals(Utils.SALES_ORDER_API_NAME, entity.getApiName())) {
                        dataDocument.put("product_price", rootBom.get(ADJUST_PRICE, String.class));
                    }
                }
            } else {
                for (Map.Entry<String, ObjectDataDocument> entry : detailDataMap.entrySet()) {
                    ObjectDataDocument dataDocument = entry.getValue();
                    if (Objects.nonNull(dataDocument)) {
                        if (Objects.equals(Utils.SALES_ORDER_API_NAME, entity.getApiName())) {
                            dataDocument.put("product_price", rootBom.get(ADJUST_PRICE, String.class));
                        }
                        break;
                    }
                }
            }
            BatchCalculate.Result result = calculateService.batchCalculate(calculateArg, serviceContext);
            Map<String, ObjectDataDocument> salesOrderProductObj = result.getCalculateResult().get(Utils.SALES_ORDER_PRODUCT_API_NAME);
            if (CollectionUtils.isEmpty(salesOrderProductObj.values())) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_PRICE_CALCULATION_INTERFACE_DID_NOT_RETURN_RESULT));
            }
            if (StringUtils.isNotBlank(entity.getRootRowId())) {
                ObjectDataDocument dataDocument = salesOrderProductObj.get(entity.getRootRowId());
                if (Objects.nonNull(dataDocument.get("subtotal"))) {
                    subtotal = dataDocument.toObjectData().get("subtotal", BigDecimal.class);
                }
            } else {
                for (Map.Entry<String, ObjectDataDocument> entry : salesOrderProductObj.entrySet()) {
                    ObjectDataDocument dataDocument = entry.getValue();
                    if (Objects.nonNull(dataDocument.get("subtotal"))) {
                        subtotal = dataDocument.toObjectData().get("subtotal", BigDecimal.class);
                        break;
                    }
                }
            }
        }
        //差额
        BigDecimal balance = rootBom.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).
                multiply(rootBom.get(PRICE_BOOK_DISCOUNT, BigDecimal.class, new BigDecimal("0"))).
                multiply(new BigDecimal(StringUtils.isBlank(entity.getRootAmount()) ? "1" : entity.getRootAmount())).
                divide(new BigDecimal("100")).subtract(subtotal);
        rootBom.set("subtotal", subtotal);
        rootBom.set(NODE_BALANCE, balance);
        return balance;

    }

    /**
     * 计算子件的标准选配价格和选配折扣
     *
     * @param resultList
     * @param bomPriceConfig
     * @param allList
     */
    private void calculateNodePriceAndDiscount(List<IObjectData> resultList, boolean bomPriceConfig, int nodeDiscountScale, int productPriceScale, List<IObjectData> allList) {
        Map<String, IObjectData> bomMap = Maps.newHashMap();
        Multimap<String, IObjectData> parentMap = ArrayListMultimap.create();
        for (IObjectData objectData : allList) {
            if (StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class)) && StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_PROD_PKG_KEY, String.class))) {
                bomMap.put(objectData.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), objectData);
                parentMap.put(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class), objectData);
                objectData.set(NODE_PRICE, Boolean.TRUE.equals(objectData.get(NON_USER_SELECTED_NODE)) ? objectData.get(ADJUST_PRICE, String.class, "0") : objectData.get(MODIFY_PRICE, String.class, "0"));
            }
        }
        Set<String> ids = Sets.newHashSet();
        for (Map.Entry<String, IObjectData> entry : bomMap.entrySet()) {
            Collection<IObjectData> objectDataList = parentMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(objectDataList)) {
                IObjectData value = entry.getValue();
                String oriPrice = Boolean.TRUE.equals(value.get(NON_USER_SELECTED_NODE)) ? value.get(ADJUST_PRICE, String.class, "0") : value.get(MODIFY_PRICE, String.class, "0");
                value.set(NODE_PRICE, oriPrice);
                ids.add(value.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
            }
        }

        Set<String> parentIds;
        int count = 0;
        do {
            parentIds = Sets.newHashSet();
            for (String id : ids) {
                IObjectData objectData = bomMap.get(id);
                if (objectData != null) {
                    calculateParentNodePrice(bomPriceConfig, bomMap, objectData);
                    if (StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class))) {
                        parentIds.add(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class));
                    }
                }
            }
            ids = parentIds;
            count++;
        } while (CollectionUtils.isNotEmpty(ids) && count < 100);

        resultList.parallelStream().forEach(x -> {
            BigDecimal nodePrice = x.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal adjustPrice = new BigDecimal(x.get(ADJUST_PRICE, String.class, "0"));
            if (nodePrice.compareTo(BigDecimal.ZERO) == 0) {
                x.set(NODE_DISCOUNT, 0);
            } else {
                //选配折扣
                x.set(NODE_DISCOUNT, adjustPrice.multiply(new BigDecimal("100")).divide(nodePrice, nodeDiscountScale, BigDecimal.ROUND_HALF_UP));
            }
            x.set(ADJUST_PRICE, adjustPrice.setScale(productPriceScale, BigDecimal.ROUND_HALF_UP));
        });
    }

    private void calculateParentNodePrice(boolean bomPriceConfig, Map<String, IObjectData> bomMap, IObjectData child) {
        String parentKey = child.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class);
        if (StringUtils.isBlank(parentKey)) {
            return;
        }
        IObjectData parentData = bomMap.get(parentKey);
        if (Objects.isNull(parentData)) {
            return;
        }
        BigDecimal balance = BigDecimal.ZERO;
        String oriAmount = Boolean.TRUE.equals(child.get(NON_USER_SELECTED_NODE)) ? child.get(AMOUNT, String.class, "0") : child.get(MODIFY_AMOUNT, String.class, "0");
        if (bomPriceConfig) {
            if (!Boolean.TRUE.equals(child.get(NON_USER_SELECTED_NODE))) {
                balance = child.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(oriAmount));
            }
        } else {
            if (child.get("selected_by_default", Boolean.class, false)) {
                if (Boolean.TRUE.equals(child.get(NON_USER_SELECTED_NODE))) {
                    balance = balance.subtract(child.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(oriAmount)));
                }
            } else {
                balance = child.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(oriAmount));
            }
        }
        parentData.set(NODE_PRICE, parentData.get(NODE_PRICE, BigDecimal.class, BigDecimal.ZERO).add(balance));
    }


    private void calculateNodeField(List<IObjectData> resultList, IObjectData bom, BigDecimal rootDiscount, BigDecimal balance, BigDecimal rootAmount, int salePriceScale, int discountScale, int nodeSubtotalScale, boolean periodPlugin) {
        //价目表折扣
        bom.set(PRICE_BOOK_DISCOUNT, rootDiscount);
        BigDecimal shareBalance = bom.get(SHARE_RATE, BigDecimal.class, BigDecimal.ZERO).multiply(balance).divide(new BigDecimal("100"));
        bom.set(NODE_BALANCE, shareBalance);
        //子件小计
        BigDecimal price = periodPlugin ? bom.get(PRICE_PER_SET, BigDecimal.class, BigDecimal.ZERO) : bom.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO);
        bom.set(NODE_SUBTOTAL, price.multiply(rootDiscount).divide(new BigDecimal("100")).multiply(rootAmount).multiply(bom.get(AMOUNT, BigDecimal.class, BigDecimal.ONE)));
        if (balance.compareTo(BigDecimal.ZERO) != 0 && StringUtils.isNotBlank(bom.get(SHARE_RATE, String.class))) {
            bom.set(NODE_SUBTOTAL, bom.get(NODE_SUBTOTAL, BigDecimal.class).subtract(shareBalance));
        }
        bom.set(NODE_SUBTOTAL, bom.get(NODE_SUBTOTAL, BigDecimal.class).setScale(nodeSubtotalScale, BigDecimal.ROUND_HALF_UP));
        //销售单价
        if (StringUtils.isNotBlank(bom.get(AMOUNT, String.class)) && bom.get(AMOUNT, BigDecimal.class).compareTo(BigDecimal.ZERO) != 0) {
            if (bom.get(NODE_SUBTOTAL, BigDecimal.class).compareTo(BigDecimal.ZERO) == 0) {
                bom.set(SALES_PRICE, 0);
            } else {
                bom.set(SALES_PRICE, bom.get(NODE_SUBTOTAL, BigDecimal.class).divide(bom.get(AMOUNT, BigDecimal.class).multiply(rootAmount), salePriceScale, BigDecimal.ROUND_HALF_UP));
            }
            //折扣
            if (StringUtils.isNotBlank(bom.get(ADJUST_PRICE, String.class)) && bom.get(ADJUST_PRICE, BigDecimal.class).compareTo(BigDecimal.ZERO) != 0) {
                bom.set("discount", bom.get(SALES_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(bom.get(ADJUST_PRICE, BigDecimal.class), discountScale, BigDecimal.ROUND_HALF_UP));
            }
        } else {
            log.warn("数量异常:{}", bom.get(AMOUNT, String.class));
        }
        resultList.parallelStream().filter(x -> Objects.equals(bom.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY))).forEach(x -> calculateNodeField(resultList, x, rootDiscount, bom.get(NODE_BALANCE, BigDecimal.class, new BigDecimal("0")), rootAmount, salePriceScale, discountScale, nodeSubtotalScale, periodPlugin));
    }

    private void getPackagePrice(QueryBomPriceModel.Param entity, List<IObjectData> resultList) {
        BigDecimal totalNodePrice = BigDecimal.ZERO;
        IObjectData rootBomData = null;
        //计算父节点为根节点的节点价格浮动
        for (IObjectData objectData : resultList) {
            if (Objects.equals(objectData.get(BomConstants.FIELD_PARENT_BOM_ID), entity.getRootBomId())) {
                totalNodePrice = totalNodePrice.add(multiply(objectData));
            }
            if (Objects.equals(objectData.getId(), entity.getRootBomId())) {
                rootBomData = objectData;
            }
        }
        //计算包的价格
        if (Objects.nonNull(rootBomData)) {
            rootBomData.set(MODIFY_PRICE, new BigDecimal(rootBomData.get(ADJUST_PRICE, String.class, "0")));
            rootBomData.set(ADJUST_PRICE, new BigDecimal(rootBomData.get(ADJUST_PRICE, String.class, "0")).add(totalNodePrice));
            rootBomData.set(NODE_PRICE, rootBomData.get(ADJUST_PRICE));
        }
    }

    private List<IObjectData> choiceDataList(QueryBomPriceModel.Param entity, Map<String, IObjectData> dataMap, Map<String, QueryBomPriceModel.BomInfo> tmpMap) {
        Set<String> allBomIds = dataMap.keySet();
        final Set<IObjectData> tmpResultList = Sets.newHashSet();
        entity.getBomList().stream().filter(x -> allBomIds.contains(x.getBomId())).forEach(x -> {
            IObjectData objectData = dataMap.get(x.getBomId());
            if (objectData != null) {
                QueryBomPriceModel.BomInfo tmpObj = tmpMap.get(x.getBomId());
                if (tmpObj != null) {
                    objectData.set(SalesOrderProductField.PROD_PKG.getApiName(), tmpObj.getProdKey());
                    objectData.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), tmpObj.getParentProdKey());
                    objectData.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), tmpObj.getRootProdKey());
                }
                tmpResultList.add(objectData);
            }
        });
        return Lists.newArrayList(tmpResultList);
    }

    private List<IObjectData> choiceDataListByNewBomPath(QueryBomPriceModel.Param entity, List<IObjectData> dataList, Map<String, IObjectData> dataMap) {
        Set<String> allBomPaths = dataMap.keySet();
        final Set<IObjectData> tmpResultList = Sets.newHashSet();
        //由于前端根节点没有传new_bom_path过来，此处不包含跟节点，
        entity.getBomList().stream().filter(x -> StringUtils.isNotBlank(x.getNewBomPath())).forEach(x -> {
            //存在的节点才加，且不是临时节点
            if (allBomPaths.contains(x.getNewBomPath()) && !Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType())) {
                IObjectData data = dataMap.get(x.getNewBomPath());
                data.set(SalesOrderProductField.PROD_PKG.getApiName(), x.getProdKey());
                data.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), x.getParentProdKey());
                data.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), x.getRootProdKey());
                tmpResultList.add(data);
            }
        });

        //临时子件直接添加到结果集中
        for (IObjectData x : dataList) {
            if (Objects.equals(EnumUtil.nodeType.temp.getValue(), x.get(BomConstants.FIELD_NODE_TYPE, String.class))) {
                tmpResultList.add(x);
                dataMap.put(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class), x);
            }
        }

        //将数据库中查询出来的根节点加入到结果集中，同时设置prod_pkg和root_prod_pkg
        for (IObjectData data : dataList) {
            if (Objects.equals(data.getId(), entity.getRootBomId())) {
                for (QueryBomPriceModel.BomInfo info : entity.getBomList()) {
                    if (Objects.equals(info.getBomId(), entity.getRootBomId())) {
                        data.set(SalesOrderProductField.PROD_PKG.getApiName(), info.getProdKey());
                        data.set(SalesOrderProductField.PARENT_PROD_PKG.getApiName(), info.getParentProdKey());
                        data.set(SalesOrderProductField.ROOT_PROD_PKG.getApiName(), info.getRootProdKey());
                        tmpResultList.add(data);
                        break;
                    }
                }
                break;
            }
        }

        return Lists.newArrayList(tmpResultList);
    }

    private void getChildNodePriceByNewBomPath(QueryBomPriceModel.Param entity, List<IObjectData> resultList, Map<String, IObjectData> dataMap, List<IObjectData> dataList, boolean bomPriceConfig, String tenantId) {
        Multimap<String, QueryBomPriceModel.BomInfo> childMap = ArrayListMultimap.create();
        QueryBomPriceModel.BomInfo rootBomInfo = null;
        Map<String, QueryBomPriceModel.BomInfo> paramMap = new HashMap<>();
        List<QueryBomPriceModel.BomInfo> updateBomList = Lists.newArrayList();
        for (QueryBomPriceModel.BomInfo bomInfo : entity.getBomList()) {
            paramMap.putIfAbsent(bomInfo.getNewBomPath(), bomInfo);
            if (bomInfo.isUpdateFlag()||StringUtils.isNotBlank(bomInfo.getUpdateField())) {
                updateBomList.add(bomInfo);
            }
            if (StringUtils.isBlank(bomInfo.getParentProdKey()) || Objects.equals(bomInfo.getProdKey(), bomInfo.getRootProdKey())) {
                rootBomInfo = bomInfo;
            } else {
                childMap.put(bomInfo.getParentProdKey(), bomInfo);
            }
        }
        if (Objects.nonNull(rootBomInfo)) {
            createNodeNo(childMap, rootBomInfo);
        }
        Map<String, IObjectData> resultDataMap = Maps.newHashMap();
        resultList.forEach(x -> {
            if (!Objects.equals(x.getId(), entity.getRootBomId())) {
                x.set(MODIFY_PRICE, x.get(ADJUST_PRICE, String.class, "0"));
                x.set(MODIFY_AMOUNT, x.get(AMOUNT, String.class, "1"));
                x.set(MODIFY_PRICING_PERIOD, x.get(PRICING_PERIOD, String.class, "1"));
                x.set(USER_SELECTED_NODE, true);
                QueryBomPriceModel.BomInfo bomInfo = paramMap.get(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class));
                if (bomInfo != null) {
                    if(!Objects.equals("cycle",bomInfo.getPricingMode())){
                        x.set(MODIFY_PRICING_PERIOD, "1");
                    }
                    x.set(ADJUST_PRICE, bomInfo.getPrice());
                    x.set(AMOUNT, bomInfo.getAmount());
                    x.set(PRICING_PERIOD, StringUtils.defaultIfBlank(bomInfo.getPricingPeriod(), "1"));
                    x.set(PRICE_PER_SET, Objects.isNull(bomInfo.getPricePerSet()) ? bomInfo.getPrice() : bomInfo.getPricePerSet());
                }
            } else {
                QueryBomPriceModel.BomInfo bomInfo = paramMap.get(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class));
                if (bomInfo != null) {
                    x.set(PRICING_PERIOD, StringUtils.defaultIfBlank(bomInfo.getPricingPeriod(), "1"));
                }
            }
            resultDataMap.put(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class), x);
        });
        if (CollectionUtils.isNotEmpty(updateBomList)) {
            updateBomList.sort(Comparator.comparing(QueryBomPriceModel.BomInfo::getNodeNo).reversed());
            for (QueryBomPriceModel.BomInfo updateBom : updateBomList) {
                IObjectData iObjectData = resultDataMap.get(updateBom.getNewBomPath());
                if (Objects.nonNull(iObjectData)) {
                    boolean isNotRootNode = StringUtils.isNotBlank(updateBom.getParentProdKey());
                    //过滤掉母件
                    if ((updateBom.isAdvancePriceFlag()||StringUtils.equals(updateBom.getUpdateField(),EnumUtil.updateField.PRICE.getValue())) && isNotRootNode) {
                        iObjectData.set(ADJUST_PRICE, updateBom.getPrice());
                    }
                    if (StringUtils.equals(updateBom.getUpdateField(),EnumUtil.updateField.PRICEPERSET.getValue())) {
                        iObjectData.set(PRICE_PER_SET, updateBom.getPricePerSet());
                    }
                    iObjectData.set(AMOUNT, updateBom.getAmount());
                }
                List<String> parentNewBomPath = Lists.newArrayList();
                String path = Optional.ofNullable(dataMap.get(updateBom.getNewBomPath())).map(r -> r.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class)).orElse("");
                if (StringUtils.isNotBlank(path)) {
                    //a.b.c.d -> (a, a.b, a.b.c) 都是它的父节点
                    List<String> pids = Splitter.on(".").splitToList(path);
                    for (int i = 0; i < pids.size(); i++) {
                        String tmpPath = String.join(".", pids.subList(0, i));
                        if (!Objects.equals(entity.getRootBomId(), tmpPath)) {
                            parentNewBomPath.add(tmpPath);
                        }
                    }
                }
                calculateNodePricePerSet(updateBom, resultDataMap.get(updateBom.getNewBomPath()), dataList, bomPriceConfig,childMap);
                parentNewBomPath.removeIf(r -> Objects.equals(entity.getRootBomId(), r) || StringUtils.isBlank(r));
                if (CollectionUtils.isNotEmpty(parentNewBomPath)) {
                    if (bomPriceConfig) {
                        parentNewBomPath.forEach(x -> Optional.ofNullable(dataMap.get(x))
                                .ifPresent(r -> {
                                    r.set(ADJUST_PRICE, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0")));
                                    r.set(PRICE_PER_SET, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0")).multiply(new BigDecimal(r.get(PRICING_PERIOD, String.class, "1"))));
                                }));
                    } else {
                        parentNewBomPath.forEach(x -> {
                            String nodeId = Optional.ofNullable(dataMap.get(x)).map(DBRecord::getId).orElse("");
                            Tuple<BigDecimal, BigDecimal> defaultSubNodePrice = getDefaultSubNodePrice(nodeId, dataList);
                            Optional.ofNullable(dataMap.get(x))
                                    .ifPresent(r -> {
                                        r.set(ADJUST_PRICE, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0")).subtract(defaultSubNodePrice.getKey()));
                                        r.set(PRICE_PER_SET, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0")).multiply(new BigDecimal(r.get(PRICING_PERIOD, String.class, "1"))).subtract(defaultSubNodePrice.getValue()));
                                    });
                        });
                    }
                    //从最底层开始算起
                    for (int i = parentNewBomPath.size() - 1; i >= 0; i--) {
                        IObjectData node = resultDataMap.get(parentNewBomPath.get(i));
                        if (node == null) {
                            continue;
                        }
                        calculatePrice(resultList, node,updateBom);
                    }
                }
            }
        }
        calculateRootPricePerSet(entity, dataList, bomPriceConfig, resultDataMap.get(entity.getRootBomId()));
        setPricePerSetScale(entity, resultList, tenantId);
    }

    private void createNodeNo(Multimap<String, QueryBomPriceModel.BomInfo> childMap, QueryBomPriceModel.BomInfo rootBomInfo) {
        rootBomInfo.setNewPath(rootBomInfo.getBomId());
        rootBomInfo.setNodeNo(1);
        Collection<QueryBomPriceModel.BomInfo> bomInfos = childMap.get(rootBomInfo.getProdKey());
        for (QueryBomPriceModel.BomInfo x : bomInfos) {
            x.setNewPath(rootBomInfo.getBomId().concat(".").concat(x.getBomId()));
            x.setNodeNo(rootBomInfo.getNodeNo() + 1);
            createProdKeyPath(childMap, x, x.getProdKey());
        }
    }

    private void setPricePerSet(QueryBomPriceModel.Param entity, List<IObjectData> resultList, List<IObjectData> dataList, boolean bomPriceConfig, String tenantId, Map<String, IObjectData> resultDataMap, Multimap<String, QueryBomPriceModel.BomInfo> childMap) {
        List<QueryBomPriceModel.BomInfo> bomList = entity.getBomList();
        if(CollectionUtils.isNotEmpty(bomList)){
            bomList.sort(Comparator.comparing(QueryBomPriceModel.BomInfo::getNodeNo).reversed());
            for (QueryBomPriceModel.BomInfo bomInfo : bomList) {
                if (Objects.equals(bomInfo.getBomId(), entity.getRootBomId())) {
                    calculateRootPricePerSet(entity, dataList, bomPriceConfig, resultDataMap.get(entity.getRootBomId()));
                } else {
                    calculateNodePricePerSet(bomInfo, resultDataMap.get(bomInfo.getNewBomPath()), dataList, bomPriceConfig,childMap);
                }
            }
            setPricePerSetScale(entity, resultList, tenantId);
        }
    }

    private void setPricePerSetScale(QueryBomPriceModel.Param entity, List<IObjectData> resultList, String tenantId) {
        if (StringUtils.isNotBlank(entity.getDetailApiName())) {
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(tenantId, entity.getApiName(), DmDefineConstants.PERIOD_PRODUCT);
            if(Objects.nonNull(pluginParam)){
                DomainPluginDescribeExt domainExt = DomainPluginDescribeExt.of(entity.getApiName(), pluginParam);
                if (Objects.equals(domainExt.getDefaultDetailObjectApiName(),entity.getDetailApiName())) {
                    IObjectDescribe detailObject = serviceFacade.findObject(tenantId, entity.getDetailApiName());
                    String fieldApiName = domainExt.getDefaultDetailFieldApiName(PRICE_PER_SET);
                    if (StringUtils.isNotBlank(fieldApiName)) {
                        Integer decimalPlaces = Optional.ofNullable(detailObject).map(x -> x.getFieldDescribe(fieldApiName)).map(x -> x.get("decimal_places", Integer.class, 2)).orElse(2);
                        resultList.forEach(x->x.set(PRICE_PER_SET,x.get(PRICE_PER_SET, BigDecimal.class,BigDecimal.ZERO).setScale(decimalPlaces,BigDecimal.ROUND_HALF_UP)));
                    }
                }

            }
        }
    }

    private void calculateRootPricePerSet(QueryBomPriceModel.Param entity, List<IObjectData> dataList, boolean bomPriceConfig, IObjectData rootData) {
        if (Objects.isNull(rootData)) {
            log.warn("root node is null id {}:",entity.getRootBomId());
            return;
        }
        BigDecimal balancePricePerSet = calculateBalancePricePerSet(dataList, bomPriceConfig, rootData);
        if (Objects.nonNull(rootData)) {
            rootData.set(DEFAULT_PRICE_PER_SET, new BigDecimal(rootData.get("origin_selling_price", String.class, "0")).multiply(new BigDecimal(rootData.get(PRICING_PERIOD, String.class, "1"))));
            rootData.set(PRICE_PER_SET, rootData.get(DEFAULT_PRICE_PER_SET, BigDecimal.class, BigDecimal.ZERO).add(balancePricePerSet));
            rootData.set(BALANCE_PRICE_PER_SET, balancePricePerSet);
        } else {
            log.warn("can not find root data:{}", entity.getRootBomId());
        }
    }

    private BigDecimal calculateBalancePricePerSet(List<IObjectData> dataList, boolean bomPriceConfig, IObjectData nodeData) {
        BigDecimal balancePricePerSet = BigDecimal.ZERO;
        for (IObjectData x : dataList) {
            if (Objects.equals(x.get(SalesOrderProductField.PARENT_PROD_PKG.getApiName()), nodeData.get(SalesOrderProductField.PROD_PKG.getApiName(), String.class))) {
                if (BooleanUtils.isTrue(x.get(USER_SELECTED_NODE, Boolean.class))) {
                    BigDecimal defaultPricePerSet = new BigDecimal(x.get(MODIFY_PRICE, String.class, "0")).multiply(new BigDecimal(x.get(MODIFY_PRICING_PERIOD, String.class, "1"))).multiply(new BigDecimal(x.get(MODIFY_AMOUNT, String.class, "1")));
                    if (bomPriceConfig) {
                        balancePricePerSet = balancePricePerSet.add(x.get(PRICE_PER_SET, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(x.get(AMOUNT, String.class, "1"))));
                    } else {
                        if (Boolean.TRUE.equals(x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT))) {
                            balancePricePerSet = balancePricePerSet.add(x.get(PRICE_PER_SET, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(x.get(AMOUNT, String.class, "1"))).subtract(defaultPricePerSet));
                        } else {
                            balancePricePerSet = balancePricePerSet.add(x.get(PRICE_PER_SET, BigDecimal.class, BigDecimal.ZERO).multiply(new BigDecimal(x.get(AMOUNT, String.class, "1"))));
                        }
                    }
                } else {
                    BigDecimal defaultPricePerSet = new BigDecimal(x.get(ADJUST_PRICE, String.class, "0")).multiply(new BigDecimal(x.get(PRICING_PERIOD, String.class, "1"))).multiply(new BigDecimal(x.get(AMOUNT, String.class, "1")));
                    if (!bomPriceConfig && Boolean.TRUE.equals(x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT))) {
                        balancePricePerSet = balancePricePerSet.subtract(defaultPricePerSet);
                    }
                }
            }
        }
        return balancePricePerSet;
    }

    private void calculateNodePricePerSet(QueryBomPriceModel.BomInfo updateBom, IObjectData iObjectData, List<IObjectData> dataList, boolean bomPriceConfig, Multimap<String, QueryBomPriceModel.BomInfo> childMap) {
        if (Objects.isNull(iObjectData) || Objects.isNull(updateBom) || !StringUtils.equalsAny(updateBom.getUpdateField(),EnumUtil.updateField.PRICE.getValue(),EnumUtil.updateField.PRICINGPERIOD.getValue())) {
            return;
        }
        BigDecimal balancePricePerSet = calculateBalancePricePerSet(dataList, bomPriceConfig, iObjectData);
        BigDecimal price = CollectionUtils.isEmpty(childMap.get(updateBom.getProdKey()))?new BigDecimal(iObjectData.get(ADJUST_PRICE, String.class, "0")):new BigDecimal(iObjectData.get(MODIFY_PRICE, String.class, "0"));
        iObjectData.set(PRICE_PER_SET,
                price.multiply(new BigDecimal(StringUtils.defaultIfBlank(updateBom.getPricingPeriod(), "1")))
                        .add(balancePricePerSet));
        iObjectData.set(BALANCE_PRICE_PER_SET, balancePricePerSet);
    }

    private void getChildNodePrice(QueryBomPriceModel.Param entity, List<IObjectData> resultList, Map<String, IObjectData> dataMap, List<IObjectData> dataList, boolean bomPriceConfig) {
        Multimap<String, QueryBomPriceModel.BomInfo> childMap = ArrayListMultimap.create();
        Map<String, QueryBomPriceModel.BomInfo> paramMap = new HashMap<>();
        QueryBomPriceModel.BomInfo rootBomInfo = null;
        for (QueryBomPriceModel.BomInfo bomInfo : entity.getBomList()) {
            paramMap.putIfAbsent(bomInfo.getBomId(), bomInfo);
            if (StringUtils.isBlank(bomInfo.getParentProdKey()) || Objects.equals(bomInfo.getProdKey(), bomInfo.getRootProdKey())) {
                rootBomInfo = bomInfo;
            } else {
                childMap.put(bomInfo.getParentProdKey(), bomInfo);
            }
        }
        if (Objects.nonNull(rootBomInfo)) {
            createNodeNo(childMap, rootBomInfo);
        }
        resultList.stream().filter(x -> !Objects.equals(x.getId(), entity.getRootBomId())).forEach(x -> {
            x.set(MODIFY_PRICE, x.get(ADJUST_PRICE, String.class, "0"));
            x.set(MODIFY_AMOUNT, x.get(AMOUNT, String.class, "0"));
            x.set(USER_SELECTED_NODE, true);
            QueryBomPriceModel.BomInfo bomInfo = paramMap.get(x.getId());
            if (bomInfo != null) {
                x.set(ADJUST_PRICE, bomInfo.getPrice());
                x.set(AMOUNT, bomInfo.getAmount());
            }
        });

        entity.getBomList().stream().filter(QueryBomPriceModel.BomInfo::isUpdateFlag).sorted(Comparator.comparing(QueryBomPriceModel.BomInfo::getNodeNo).reversed()).forEach(d -> {
            IObjectData iObjectData = dataMap.get(d.getBomId());
            if (Objects.nonNull(iObjectData)) {
                if (d.isAdvancePriceFlag()) {
                    iObjectData.set(ADJUST_PRICE, d.getPrice());
                }
                iObjectData.set(AMOUNT, d.getAmount());
            }
            List<String> parentIds = Lists.newArrayList();
            String path = d.getNewPath();
            if (StringUtils.isNotBlank(path)) {
                parentIds.addAll(Splitter.on(".").splitToList(StringUtils.substringBeforeLast(path, ".")));
            }
            parentIds.removeIf(r -> Objects.equals(entity.getRootBomId(), r));
            if (CollectionUtils.isNotEmpty(parentIds)) {
                if (bomPriceConfig) {
                    for (String x : parentIds) {
                        Optional.ofNullable(dataMap.get(x))
                                .ifPresent(r -> r.set(ADJUST_PRICE, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0"))));
                    }
                } else {
                    parentIds.forEach(x -> {
                        Tuple<BigDecimal, BigDecimal> defaultSubNodePrice = getDefaultSubNodePrice(x, dataList);
                        Optional.ofNullable(dataMap.get(x))
                                .ifPresent(r -> r.set(ADJUST_PRICE, new BigDecimal(r.get(MODIFY_PRICE, String.class, "0")).subtract(defaultSubNodePrice.getKey())));
                    });
                }
                for (int i = parentIds.size() - 1; i >= 0; i--) {
                    String parentId = parentIds.get(i);
                    QueryBomPriceModel.BomInfo bomInfo = paramMap.get(parentId);
                    if (Objects.nonNull(bomInfo) && bomInfo.isAdvancePriceFlag()) {
                        break;
                    }
                    resultList.stream().filter(x -> Objects.equals(x.getId(), parentId)).findFirst().ifPresent(x -> calculatePrice(resultList, x,d));
                }
            }
        });
    }

    private void createProdKeyPath(Multimap<String, QueryBomPriceModel.BomInfo> childMap, QueryBomPriceModel.BomInfo bomInfo, String prodKey) {
        Collection<QueryBomPriceModel.BomInfo> bomInfos = childMap.get(bomInfo.getProdKey());
        for (QueryBomPriceModel.BomInfo x : bomInfos) {
            x.setNewPath(bomInfo.getNewPath().concat(".").concat(x.getBomId()));
            x.setNodeNo(bomInfo.getNodeNo() + 1);
            createProdKeyPath(childMap, x, prodKey);
            if (StringUtils.equals(x.getProdKey(), prodKey)) {
                break;
            }
        }
    }


    private Tuple<BigDecimal, BigDecimal> getDefaultSubNodePrice(String nodeId, List<IObjectData> bomList) {
        BigDecimal nodePrice = BigDecimal.ZERO;
        BigDecimal nodePricePerSet = BigDecimal.ZERO;
        Predicate<IObjectData> cond = x ->
                StringUtils.isNotBlank(nodeId)
                        && nodeId.equals(x.get("parent_bom_id"))
                        && x.get("selected_by_default", Boolean.class, false);
        for (IObjectData r : bomList) {
            if (cond.test(r)) {
                if (r.get(USER_SELECTED_NODE, Boolean.class, false)) {
                    nodePrice = nodePrice.add(multiplyNew(r));
                    nodePricePerSet = nodePricePerSet.add(multiplyNew(r).multiply(new BigDecimal(r.get(MODIFY_PRICING_PERIOD, String.class, "1"))));
                } else {
                    nodePrice = nodePrice.add(multiply(r));
                    nodePricePerSet = nodePricePerSet.add(multiply(r).multiply(new BigDecimal(r.get(PRICING_PERIOD, String.class, "1"))));
                }
            }
        }
        return Tuple.of(nodePrice, nodePricePerSet);
    }

    private void fillLookUpField(ServiceContext context, List<IObjectData> dataList, List<IObjectData> bomList, RequestContext requestContext, IObjectDescribe bomDescribe) {
        RequestContextManager.setContext(requestContext);
        //添加lookup字段的主属性__r
        serviceFacade.fillObjectDataWithRefObject(bomDescribe, dataList, context.getUser(), null);
        IObjectDescribe productObjDesc = serviceFacade.findObject(context.getTenantId(), Utils.PRODUCT_API_NAME);
        IFieldDescribe fieldDescribe = productObjDesc.getFieldDescribe(CommonProductConstants.Field.Unit.apiName);
        List<Map> optionsList = fieldDescribe.get("options", List.class);
        List<IObjectData> productList = serviceFacade.findObjectDataByIdsIgnoreFormula(context.getTenantId(), dataList.stream().map(x -> x.get("product_id", String.class)).collect(Collectors.toList()), Utils.PRODUCT_API_NAME);
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        String unitName = CommonProductConstants.Field.Unit.apiName + "__r";
        List<String> bomPath = Lists.newArrayList();
        Map<String, IObjectData> dataMap = Maps.newHashMap();
        for (IObjectData data : productList) {
            data.set(unitName,
                    getOptionLabel(optionsList, data.get(CommonProductConstants.Field.Unit.apiName, String.class)));
            dataMap.put(data.getId(), data);
        }
        for (IObjectData data : dataList) {
            IObjectData productObj = dataMap.get(data.get("product_id"));
            data.set("product_id__ro", ObjectDataDocument.of(productObj));
            if (StringUtils.isBlank(data.get(PRODUCT_ID_R, String.class))) {
                data.set(PRODUCT_ID_R, Optional.ofNullable(productObj).map(IObjectData::getName).orElse(null));
            }
            /*if (!Objects.equals(ProductConstants.Status.ON.getStatus(), Optional.ofNullable(productObj).map(x -> x.get("product_status", String.class, "")).orElse(""))) {
                bomPath.add(data.get(BomConstants.FIELD_BOM_PATH, String.class, ""));
            }*/
            if (Objects.equals(EnumUtil.nodeType.temp.getValue(), data.get("node_type", String.class)) && data.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 0) == EnumUtil.PriceMode.DEF.getValue()) {
                data.set(BomConstants.FIELD_ADJUST_PRICE, Optional.ofNullable(productObj).map(x -> x.get("price")).orElse(0));
            }
        }

    }



    //计算价格和数量的乘机
    private BigDecimal multiply(IObjectData x) {
        return new BigDecimal(x.get(ADJUST_PRICE, String.class, "0")).multiply(new BigDecimal(x.get(AMOUNT, String.class, "0")));
    }

    private BigDecimal multiplyNew(IObjectData x) {
        return new BigDecimal(x.get(MODIFY_PRICE, String.class, "0")).multiply(new BigDecimal(x.get(MODIFY_AMOUNT, String.class, "0")));
    }

    private void calculatePrice(List<IObjectData> bomList, IObjectData bom, QueryBomPriceModel.BomInfo updateBom) {
        bomList.stream().
                filter(x -> Objects.equals(bom.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY))).  //获取当前节点的子节点
                forEach(x -> {
            BigDecimal price = new BigDecimal(bom.get(ADJUST_PRICE, String.class, "0")).add(multiply(x));
            BigDecimal pricePerSet = new BigDecimal(bom.get(PRICE_PER_SET, String.class, "0")).add(new BigDecimal(x.get(PRICE_PER_SET, String.class, "0")).multiply(new BigDecimal(x.get(AMOUNT, String.class, "1"))));
            if (!StringUtils.equals(updateBom.getUpdateField(), EnumUtil.updateField.PRICINGPERIOD.getValue())) {
                bom.set(ADJUST_PRICE, price);
            }
            bom.set(PRICE_PER_SET, pricePerSet);
        });
    }


    private SearchTemplateQuery query(String rootId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(2000);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, BomConstants.FIELD_ROOT_ID, rootId);
        searchQuery.setPermissionType(0);
        return searchQuery;
    }

    private void getBomPriceByCond(List<IObjectData> bomList, QueryBomPriceModel.Param arg, User user) {
        if (StringUtils.isBlank(arg.getAccountId())) {
            return;
        }
        List<IObjectData> subList = bomList.stream().filter(x -> subFilter(arg, x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subList)) {
            getPrice(bomList, arg, user, subList);
        }
    }

    private boolean subFilter(QueryBomPriceModel.Param arg, IObjectData x) {
        return x.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue() || Objects.equals(x.getId(), arg.getRootBomId());
    }

    private void getPrice(List<IObjectData> bomList, QueryBomPriceModel.Param arg, User user, List<IObjectData> newBomList) {
        RealPriceModel.Arg param = new RealPriceModel.Arg();
        param.setAccountId(arg.getAccountId());
        param.setPartnerId(arg.getPartnerId());
        param.setMcCurrency(arg.getMcCurrency());
        List<RealPriceModel.FullProduct> fullProductList = Lists.newArrayList();
        QueryBomPriceModel.BomInfo rootBomInfo = arg.getBomList().stream().filter(x -> Objects.equals(arg.getRootBomId(), x.getBomId())).findFirst().orElse(null);
        String rootPriceBookId = Optional.ofNullable(rootBomInfo).map(QueryBomPriceModel.BomInfo::getPriceBookId).orElse("");
        boolean flag = bizConfigThreadLocalCacheService.bomAdaptationPriceListRules(user.getTenantId());
        newBomList.forEach(x -> handleParam(fullProductList, x, arg, rootPriceBookId, flag));
        if (CollectionUtils.isEmpty(fullProductList)) {
            return;
        }
        param.setObjectData(Objects.isNull(arg.getObjectData()) ? arg.getObject_data() : arg.getObjectData());
        param.setDetails(arg.getDetails());
        param.setActCurrentDetailRow(true);
        param.setFullProductList(fullProductList);
        param.setOnlySimpleSearch(Boolean.TRUE);
        param.setRootBomList(bomList);
        setBomPrice(bomList, param, user);
    }

    private void handleParam(List<RealPriceModel.FullProduct> fullProductList, IObjectData x, QueryBomPriceModel.Param arg, String rootPriceBookId, boolean flag) {
        RealPriceModel.FullProduct entity = new RealPriceModel.FullProduct();
        entity.setProductId(x.get(BomConstants.FIELD_PRODUCT_ID, String.class));
        List<Attribute> attr = (List<Attribute>) x.get("attribute");
        if (CollectionUtils.isNotEmpty(attr)) {
            Map<String, String> attrMap = Maps.newHashMap();
            attr.forEach(data -> {
                List<AttributeValue> attributeValues = data.getAttributeValues();
                if (CollectionUtils.isNotEmpty(attributeValues)) {
                    AttributeValue attributeValue = attributeValues.parallelStream().filter(p -> Objects.equals("1", p.getIs_default())).findFirst().orElse(attributeValues.get(0));
                    attrMap.put(data.getId(), attributeValue.getId());
                }
            });
            entity.setAttrMap(attrMap);
        }
        Optional<QueryBomPriceModel.BomInfo> bomInfo = arg.getBomList().stream().filter(k -> Objects.equals(x.getId() + x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class, ""), k.getBomId() + MoreObjects.firstNonNull(k.getProdKey(), ""))).findFirst();
        if (bomInfo.isPresent()) {
            Map<String, String> attrMap = bomInfo.get().getAttrMap();
            if (MapUtils.isNotEmpty(attrMap)) {
                entity.setAttrMap(attrMap);
            }
            if (!arg.isPriority()) {
                entity.setPriceBookId(bomInfo.get().getPriceBookId());
            }
        } else {
            if (!arg.isPriority() && !flag && StringUtils.isNotBlank(rootPriceBookId)) {
                entity.setPriceBookId(rootPriceBookId);
            }
        }
        entity.setRootBomId(x.get(BomConstants.FIELD_ROOT_ID, String.class, ""));
        entity.setBomId(x.getId());
        entity.setParentBomId(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class, ""));
        entity.setUnit(x.get(BomConstants.FIELD_UNIT_ID, String.class, ""));
        fullProductList.add(entity);
    }

    private void setBomPrice(List<IObjectData> bomList, RealPriceModel.Arg param, User user) {
        RealPriceModel.Result realPrice = availableRangeCoreService.getRealPrice(user, param);
        List<ObjectDataDocument> newRst = realPrice.getNewRst();
        Map<String, ObjectDataDocument> rstMap = newRst.stream().collect(Collectors.toMap(k -> String.valueOf(k.get(BomConstants.FIELD_BOM_ID)), v -> v, (k1, k2) -> k1));
        for (IObjectData x : bomList) {
            if (x.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue() || Objects.equals(x.getId(), x.get("root_id", String.class))) {
                ObjectDataDocument r = rstMap.get(x.getId());
                if (Objects.nonNull(r)) {
                    BigDecimal sellingPrice = new BigDecimal(MapUtils.getString(r, "selling_price", "0"));
                    BigDecimal discount = Objects.equals(x.getId(), x.get("root_id", String.class)) ? BigDecimal.ONE : new BigDecimal(MapUtils.getString(r, "discount", "100")).divide(new BigDecimal(100));
                    x.set(BomConstants.FIELD_ADJUST_PRICE, sellingPrice.multiply(discount));
                    x.set(PRICE_BOOK_DISCOUNT, MapUtils.getString(r, "discount", "100"));
                    x.set(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), r.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName()));
                    x.set("price_book_product_id", r.getId());
                    if (StringUtils.isAnyBlank(r.getId(), x.get(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), String.class))) {
                        log.warn(x.getId() + "未返回价目表结果");// ignoreI18n
                    }
                    x.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), r.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()));
                    x.set("selling_price", sellingPrice);
                    x.set("origin_selling_price", new BigDecimal(MapUtils.getString(r, "origin_selling_price", "0")));
                    x.set("price_book_product_id__r", r.get("name"));
                    x.set("attribute_price_book_id", r.get("attribute_price_book_id"));
                    x.set("attribute_price_book_id__r", r.get("attribute_price_book_id__r"));
                    x.set("attribute_price_book_lines_ids", r.get("attribute_price_book_lines_ids"));
                    x.set(QuoteConstants.QuoteField.PRICEBOOKNAME.getApiName(), r.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName().concat(SystemConstants.AliasApiName.R.value)));
                } else {
                    log.warn(x.getId() + "未返回价目表结果");// ignoreI18n
                }

            }
        }
    }

    private String getOptionLabel(List<Map> optionsList, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        if (CollectionUtils.isEmpty(optionsList)) {
            return null;
        }
        for (Map option : optionsList) {
            if (Objects.equals(MapUtils.getString(option, "value", ""), key)) {
                return MapUtils.getString(option, "label");
            }
        }
        return null;
    }

    /**
     * 切换主对象计算从对象的BOM价格
     *  @param entity
     * @param resultList
     * @param dataList
     * @param bomPriceConfig
     * @param tenantId
     */
    public void switchPriceBookQueryBomPrice(QueryBomPriceModel.Param entity, List<IObjectData> resultList, List<IObjectData> dataList, boolean bomPriceConfig, String tenantId) {
        Map<String, IObjectData> dataMap = Maps.newHashMap();
        Multimap<String, QueryBomPriceModel.BomInfo> paramChildMap = ArrayListMultimap.create();
        Map<String, QueryBomPriceModel.BomInfo> paramMap = new HashMap<>();
        for (QueryBomPriceModel.BomInfo bom : entity.getBomList()) {
            paramMap.putIfAbsent(bom.getProdKey(), bom);
            paramChildMap.put(bom.getParentProdKey(), bom);
        }
        Multimap<String, IObjectData> selectedChildMap = ArrayListMultimap.create();
        Multimap<String, IObjectData> childMap = ArrayListMultimap.create();
        Map<String, IObjectData> parentMap = Maps.newHashMap();
        dataList.forEach(x -> {
            x.set(MODIFY_PRICE, x.get(ADJUST_PRICE, String.class, "0"));
            x.set(MODIFY_AMOUNT, x.get(AMOUNT, String.class, "0"));
            x.set(MODIFY_PRICING_PERIOD,x.get(PRICING_PERIOD, String.class, "1"));
            dataMap.put(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), x);
            if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))) {
                if (Boolean.TRUE.equals(x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT)) && Boolean.TRUE.equals(x.get(NON_USER_SELECTED_NODE))) {
                    selectedChildMap.put(x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class), x);
                }
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class))) {
                    parentMap.put(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), x);
                }
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class)) && !Boolean.TRUE.equals(x.get(NON_USER_SELECTED_NODE))) {
                    childMap.put(x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class), x);
                }
            }
        });
        Set<String> ids = Sets.newHashSet();
        String rootKey = "";
        Map<String, IObjectData> resultDataMap = Maps.newHashMap();
        for (IObjectData x : resultList) {
            x.set(USER_SELECTED_NODE, true);
            resultDataMap.put(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class), x);
            if(Objects.equals(x.getId(),entity.getRootBomId())){
                rootKey = x.get(BomConstants.FIELD_PROD_PKG_KEY,String.class);
            }
            Collection<IObjectData> childList = childMap.get(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
            if (CollectionUtils.isEmpty(childList)) {
                Collection<IObjectData> selectedChildList = selectedChildMap.get(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
                BigDecimal subtotal = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(selectedChildList) && !bomPriceConfig) {
                    for (IObjectData d : selectedChildList) {
                        IObjectData child = dataMap.get(d.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
                        if (child != null) {
                            BigDecimal price = child.get(MODIFY_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(child.get(MODIFY_AMOUNT, BigDecimal.class, BigDecimal.ONE));
                            subtotal = subtotal.add(price);
                        }
                    }
                }
                x.set(ADJUST_PRICE, x.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).subtract(subtotal));
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class))) {
                    ids.add(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
                }
            }
            QueryBomPriceModel.BomInfo bomInfo = paramMap.get(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
            if (Objects.nonNull(bomInfo) && StringUtils.isNotBlank(bomInfo.getAmount())) {
                x.set(AMOUNT, bomInfo.getAmount());
            }
            x.set(PRICING_PERIOD, StringUtils.defaultIfBlank(bomInfo.getPricingPeriod(),"1"));
        }
        Set<String> parentIds;
        int count = 0;
        do {
            parentIds = Sets.newHashSet();
            for (String id : ids) {
                IObjectData objectData = dataMap.get(id);
                if (objectData != null) {
                    calculateNodePrice(dataMap, selectedChildMap, parentMap, objectData, bomPriceConfig, paramMap);
                    if (StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class))) {
                        parentIds.add(objectData.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class));
                    }
                }
            }
            ids = parentIds;
            count++;
        } while (CollectionUtils.isNotEmpty(ids) && count < 100);
        QueryBomPriceModel.BomInfo rootBomInfo = paramMap.get(rootKey);
        if (Objects.nonNull(rootBomInfo)) {
            createNodeNo(paramChildMap,rootBomInfo);
            setPricePerSet(entity,resultList,dataList,bomPriceConfig,tenantId,resultDataMap,paramChildMap);
        }
    }

    //不包含默认选中等于用户修改的数量乘以标准选配价格，包含默认选中原始的价格乘以数量
    private void calculateNodePrice(Map<String, IObjectData> dataMap, Multimap<String, IObjectData> selectedChildMap, Map<String, IObjectData> parentMap, IObjectData x, boolean bomPriceConfig, Map<String, QueryBomPriceModel.BomInfo> paramMap) {
        IObjectData parentNode = parentMap.get(x.get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class));
        if (parentNode == null) {
            return;
        }
        BigDecimal subtotal;
        Collection<IObjectData> selectedChildList = selectedChildMap.get(parentNode.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
        if (bomPriceConfig) {
            subtotal = x.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(x.get(AMOUNT, BigDecimal.class, BigDecimal.ZERO));
        } else {
            if (Boolean.TRUE.equals(x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT))) {
                subtotal = x.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(x.get(AMOUNT, BigDecimal.class, BigDecimal.ZERO).subtract(x.get(MODIFY_AMOUNT, BigDecimal.class, BigDecimal.ZERO)));
            } else {
                subtotal = x.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(x.get(AMOUNT, BigDecimal.class, BigDecimal.ZERO));
            }
        }
        selectedChildList.removeIf(i -> Objects.equals(x.get(BomConstants.FIELD_PROD_PKG_KEY, String.class), i.get(BomConstants.FIELD_PROD_PKG_KEY, String.class)));
        if (CollectionUtils.isNotEmpty(selectedChildList) && !bomPriceConfig) {
            for (IObjectData d : selectedChildList) {
                IObjectData child = dataMap.get(d.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
                if (child != null) {
                    BigDecimal price = child.get(MODIFY_PRICE, BigDecimal.class, BigDecimal.ZERO).multiply(child.get(MODIFY_AMOUNT, BigDecimal.class, BigDecimal.ONE));
                    subtotal = subtotal.subtract(price);
                }
            }
            selectedChildMap.removeAll(parentNode.get(BomConstants.FIELD_PROD_PKG_KEY, String.class));
        }
        parentNode.set(ADJUST_PRICE, parentNode.get(ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO).add(subtotal));
    }

}
