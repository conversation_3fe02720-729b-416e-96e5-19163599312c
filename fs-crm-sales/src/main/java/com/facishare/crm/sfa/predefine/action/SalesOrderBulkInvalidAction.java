package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.action.listener.SalesOrderBulkInvalidActionListener;
import com.facishare.crm.sfa.predefine.service.BizCommonConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * Created by renlb on 2019/3/14.
 */
@Slf4j
public class SalesOrderBulkInvalidAction extends StandardBulkInvalidAction {
    private static final BizCommonConfigService bizCommonConfigService = SpringUtil.getContext().getBean(BizCommonConfigService.class);

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(SalesOrderBulkInvalidActionListener.class);
        return classList;
    }


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        boolean newInvoice = bizCommonConfigService.isOpen(IModuleInitService.MODULE_NEW_INVOICE, actionContext.getUser());
        if (newInvoice) {
            dataList.forEach(objectData -> {
                findNewInvoiceLineByOrderId(objectData.getId());
                String invoiceStatus = objectData.get("invoice_status", String.class);
                if (!Objects.equals("3", invoiceStatus)) {
                    throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
                }

                BigDecimal invoicedAmount = objectData.get("invoice_amount") == null ? BigDecimal.ZERO
                    : objectData.get("invoice_amount", BigDecimal.class);
                if (invoicedAmount.compareTo(BigDecimal.ZERO) > 0) {
                    throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
                }
            });
        }
    }

    private void findNewInvoiceLineByOrderId(String orderId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(10);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "order_id", orderId);
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        SearchUtil.fillFilterIn(filters, "life_status", Lists.newArrayList(
            SystemConstants.LifeStatus.UnderReview.value,
            SystemConstants.LifeStatus.Normal.value,
            SystemConstants.LifeStatus.InChange.value));
        query.setFilters(filters);
        QueryResult<IObjectData> newInvoiceLines = serviceFacade.findBySearchQuery(actionContext.getUser(), Utils.INVOICE_APPLICATION_LINES_API_NAME, query);
        if (CollectionUtils.notEmpty(newInvoiceLines.getData())) {
            List<IObjectData> data = newInvoiceLines.getData();
            if (data.size() > 0) {
                throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_INVOICED_PLEASE_INVALIDATE_INVOICE));
            }
        }
    }
}
