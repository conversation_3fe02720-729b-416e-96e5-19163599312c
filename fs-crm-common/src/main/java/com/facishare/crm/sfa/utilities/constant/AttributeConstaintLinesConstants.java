package com.facishare.crm.sfa.utilities.constant;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2023-12-15
 */
public interface AttributeConstaintLinesConstants {
    String DESC_API_NAME = "AttributeConstraintLinesObj";
    String FIELD_OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
    String FIELD_ATTRIBUTE_CONSTRAINT_ID = "attribute_constraint_id";
    String FIELD_ROOT_NODE_ID = "root_node_id";
    String FIELD_ATTRIBUTE_ID = "attribute_id";
    String FIELD_ATTRIBUTE_VALUE_IDS = "attribute_value_ids";
    String FIELD_PARENT_ID = "parent_id";
    String FIELD_IS_REQUIRED = "is_required";
    String FIELD_IS_MULTI_SELECTED = "is_multi_selected";
    String FIELD_IS_STANDARD_ATTRIBUTE = "is_standard_attribute";

    String FIELD_NAME = "name";
    String FIELD_NODE_TYPE = "node_type";
    String FIELD_NODE_LEVEL = "node_level";
    String FIELD_DEFAULT_ATTR_VALUES_IDS= "default_attr_values_ids";
    
    String FIELD_NODE_TEMP_ID = "node_temp_id";

    enum NodeType {
        ATTRIBUTE(1, "属性或非标属性"),// ignoreI18n
        ATTRIBUTE_VALUE(2, "属性值或非标属性值");// ignoreI18n

        private int value;

        private String  name;

        NodeType(int value, String name) {
            this.value = value;
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

    }

}
