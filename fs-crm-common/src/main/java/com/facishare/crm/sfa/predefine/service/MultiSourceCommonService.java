package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.describebuilder.CountFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.MultiSourceServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.DeleteRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.UpdateRule;
import com.facishare.paas.appframework.metadata.JobScheduleService;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.facishare.crm.enums.ConfigType.*;

/**
 * 迁移过来的，后续和PriceBookCommonService合并
 */

@Component
@Slf4j
public class MultiSourceCommonService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private ObjectValueMappingService objectValueMappingService;
    @Autowired
    private ConfigService configService;
    @Autowired
    protected IObjectDescribeService objectDescribeService;
    @Autowired
    JobScheduleService calculateRestService;

    public void createOrUpdateRule(ServiceContext serviceContext, CreateRule.Arg arg, String fromObjectApiName,String relatedFieldApiName) {
        if (Strings.isEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (CollectionUtils.isEmpty(arg.getRuleList())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        MappingRuleDocument mappingRuleDocument = arg.getRuleList().get(0);
        String fieldApiName = mappingRuleDocument.get("field_api_name").toString();
        if (Strings.isEmpty(fieldApiName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        mappingRuleDocument.remove("field_api_name");
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        String targetApiName = mappingRuleDocument.get("target_api_name").toString();
        if (!Objects.equals(fromObjectApiName, targetApiName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }

        IObjectDescribe sourceDescribe = serviceFacade.findObject(serviceContext.getTenantId(), sourceApiName);
        IObjectDescribe targetDescribe = serviceFacade.findObject(serviceContext.getTenantId(), targetApiName);
        buildRuleDocument(serviceContext.getUser(), mappingRuleDocument, sourceDescribe, fromObjectApiName);
        ButtonDocument buttonDocument = buildRuleButton(mappingRuleDocument, fromObjectApiName);
        arg.setButton(buttonDocument);

        String ruleApiName = mappingRuleDocument.get("rule_api_name").toString();

        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(serviceContext.getUser(), ruleApiName);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            //更新映射规则
            DeleteRule.Arg deleteArg = new DeleteRule.Arg();
            deleteArg.setDescribeApiName(sourceApiName);
            deleteArg.setRuleApiName(ruleApiName);
            objectValueMappingService.deleteRule(deleteArg, serviceContext);
            objectValueMappingService.createRule(arg, serviceContext);
            saveConfig(serviceContext.getUser(), sourceApiName, ruleApiName, fieldApiName, targetApiName);
        } else {
            objectValueMappingService.createRule(arg, serviceContext);
            String countFieldName = addCountField(serviceContext.getUser(), sourceDescribe, fieldApiName, targetApiName, relatedFieldApiName);
            if (fromObjectApiName.equals(Utils.ORDER_PAYMENT_API_NAME)) {
                updateFieldWheres(targetDescribe, countFieldName, fieldApiName, relatedFieldApiName);
            }
            saveConfig(serviceContext.getUser(), sourceApiName, ruleApiName, fieldApiName, fromObjectApiName);
        }
    }

    public void createOrUpdateRule(ServiceContext serviceContext, CreateRule.Arg arg,String bizType) {
        if (Strings.isEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (CollectionUtils.isEmpty(arg.getRuleList())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        MappingRuleDocument mappingRuleDocument = arg.getRuleList().get(0);
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        buildRuleDocument(serviceContext.getUser(), mappingRuleDocument, bizType);
        ButtonDocument buttonDocument = buildRuleButton(mappingRuleDocument);
        arg.setButton(buttonDocument);

        String ruleApiName = mappingRuleDocument.get("rule_api_name").toString();

        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(serviceContext.getUser(), ruleApiName);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            //更新映射规则
            DeleteRule.Arg deleteArg = new DeleteRule.Arg();
            deleteArg.setDescribeApiName(sourceApiName);
            deleteArg.setRuleApiName(ruleApiName);
            objectValueMappingService.deleteRule(deleteArg, serviceContext);
            objectValueMappingService.createRule(arg, serviceContext);
        } else {
            objectValueMappingService.createRule(arg, serviceContext);
        }
    }

    public void deleteOrderPaymentRule(ServiceContext serviceContext, DeleteRule.Arg arg, String fromObjectApiName, String relatedFieldApiName) {
        if (Strings.isEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (Strings.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(serviceContext.getUser(), arg.getRuleApiName());
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            objectValueMappingService.deleteRule(arg, serviceContext);
        }
        IObjectDescribe sourceDescribe = serviceFacade.findObject(serviceContext.getTenantId(), arg.getDescribeApiName());
        if (Objects.nonNull(sourceDescribe)) {
            removeCountField(sourceDescribe, relatedFieldApiName);
        }

        updateConfig(serviceContext.getUser(), arg.getDescribeApiName(), fromObjectApiName);
    }

    public void deleteRule(ServiceContext serviceContext, DeleteRule.Arg arg) {
        if (Strings.isEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (Strings.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(serviceContext.getUser(), arg.getRuleApiName());
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            objectValueMappingService.deleteRule(arg, serviceContext);
        }
    }

    private void removeCountField(IObjectDescribe describe, String relatedFieldApiName) {
        if (StringUtils.isEmpty(relatedFieldApiName)) {
            return;
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(relatedFieldApiName + "__c");
        if (Objects.isNull(fieldDescribe)) {
            return;
        }
        try {
            objectDescribeService.deleteCustomFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
        } catch (MetadataServiceException e) {
            log.error("removeCountField error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }
    private void updateConfig(User user, String describeApiName, String fromObjectApiName)
    {
        String configKey = Strings.EMPTY;
        if (Objects.equals(fromObjectApiName, Utils.ORDER_PAYMENT_API_NAME)) {
            configKey = ORDER_PAYMENT_MAPPING_RULE.getKey();
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.SettlementDetail.getApiName())) {
            configKey = SETTLEMENT_DETAIL_MAPPING_RULE.getKey();
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.InvoiceApplicationLines.getApiName())) {
            configKey = INVOICE_LINES_MAPPING_RULE.getKey();
        }
        String configValue = configService.findTenantConfig(user, configKey);
        if(Strings.isEmpty(configValue)) {
            return;
        }
        //更新
        List<MultiSourceServiceModel.MultiSourceMappingRuleModel> multiSourceMappingRuleModels
                = JSON.parseArray(configValue, MultiSourceServiceModel.MultiSourceMappingRuleModel.class);
        multiSourceMappingRuleModels.removeIf(o -> Objects.equals(describeApiName, o.getObjectApiName()));
        configService.updateTenantConfig(user, configKey, JSON.toJSONString(multiSourceMappingRuleModels), ConfigValueType.STRING);
    }

    private void buildRuleDocument(User user, MappingRuleDocument mappingRuleDocument, IObjectDescribe sourceDescribe, String fromObjectApiName) {
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        String targetApiName = mappingRuleDocument.get("target_api_name").toString();
        String ruleApiName = Strings.EMPTY;
        if (Objects.equals(fromObjectApiName, Utils.ORDER_PAYMENT_API_NAME)) {
            ruleApiName = String.format("rule_p_%s2op__c", sourceApiName.toLowerCase());
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.SettlementDetail.getApiName())) {
            ruleApiName = String.format("rule_p_%s2sd__c", sourceApiName.toLowerCase());
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.InvoiceApplicationLines.getApiName())) {
            ruleApiName = String.format("rule_%s2ial__c", sourceApiName.toLowerCase());
        }
        String ruleName = String.format("mt_%s2%s"
                , sourceDescribe.getDisplayName(), I18N.text(targetApiName + ".attribute.self.display_name"));
        mappingRuleDocument.put("rule_api_name", ruleApiName);
        mappingRuleDocument.put("rule_name", ruleName);
        mappingRuleDocument.put("master_rule_api_name", "");
        mappingRuleDocument.put("master_api_name", "");
        mappingRuleDocument.put("md_type", 0);
        mappingRuleDocument.put("package", "CRM");
        mappingRuleDocument.put("define_type", "system");
        mappingRuleDocument.put("biz_type", String.format("multi_source_%s", fromObjectApiName.toLowerCase()));
        mappingRuleDocument.put(Tenantable.TENANT_ID, user.getTenantId());
    }
    private void buildRuleDocument(User user, MappingRuleDocument mappingRuleDocument, String bizType) {
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        String ruleApiName = String.format("rule_%s_%s", bizType, sourceApiName.toLowerCase());
        String ruleName = ruleApiName;
        mappingRuleDocument.put("rule_api_name", ruleApiName);
        mappingRuleDocument.put("rule_name", ruleName);
        mappingRuleDocument.put("master_rule_api_name", "");
        mappingRuleDocument.put("master_api_name", "");
        mappingRuleDocument.put("md_type", 0);
        mappingRuleDocument.put("package", "CRM");
        mappingRuleDocument.put("define_type", "system");
        mappingRuleDocument.put("biz_type", bizType);
        mappingRuleDocument.put(Tenantable.TENANT_ID, user.getTenantId());
    }
    private ButtonDocument buildRuleButton(MappingRuleDocument mappingRuleDocument, String fromObjectApiName) {
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        ButtonDocument buttonDocument = new ButtonDocument();
        if (Objects.equals(fromObjectApiName, Utils.ORDER_PAYMENT_API_NAME)) {
            buttonDocument.put("api_name", String.format("button_p_%s2op__c", sourceApiName.toLowerCase()));
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.SettlementDetail.getApiName())) {
            buttonDocument.put("api_name", String.format("button_p_%s2sd__c", sourceApiName.toLowerCase()));
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.InvoiceApplicationLines.getApiName())) {
            buttonDocument.put("api_name", String.format("button_%s2ial__c", sourceApiName.toLowerCase()));
        }
        String buttonName = String.format("mt_2%s", sourceApiName);
        buttonDocument.put("label", buttonName);
        buttonDocument.put("description", "");
        buttonDocument.put("describe_api_name", sourceApiName);
        buttonDocument.put("use_pages", "detail");
        buttonDocument.put("button_type", "convert");
        buttonDocument.put("is_active", false);
        return buttonDocument;
    }
    private ButtonDocument buildRuleButton(MappingRuleDocument mappingRuleDocument) {
        String sourceApiName = mappingRuleDocument.get("source_api_name").toString();
        String ruleApiName = mappingRuleDocument.get("rule_api_name").toString();
        ButtonDocument buttonDocument = new ButtonDocument();
        buttonDocument.put("api_name", String.format("bt_%s2", ruleApiName));
        buttonDocument.put("label", String.format("bt_%s2", ruleApiName));
        buttonDocument.put("description", "");
        buttonDocument.put("describe_api_name", sourceApiName);
        buttonDocument.put("use_pages", "detail");
        buttonDocument.put("button_type", "convert");
        buttonDocument.put("is_active", false);
        return buttonDocument;
    }
    private UpdateRule.Arg buildUpdateRuleArg(CreateRule.Arg arg,MappingRuleDocument mappingRuleDocument) {
        UpdateRule.Arg updateRuleArg = new UpdateRule.Arg();

        updateRuleArg.setRuleList(Lists.newArrayList(mappingRuleDocument));
        updateRuleArg.setButton(arg.getButton());
        return updateRuleArg;
    }

    private void saveConfig(User user, String sourceApiName, String ruleApiName, String fieldApiName, String fromObjectApiName) {
        String configKey = Strings.EMPTY;
        if (Objects.equals(fromObjectApiName, Utils.ORDER_PAYMENT_API_NAME)) {
            configKey = ORDER_PAYMENT_MAPPING_RULE.getKey();
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.SettlementDetail.getApiName())) {
            configKey = SETTLEMENT_DETAIL_MAPPING_RULE.getKey();
        } else if (Objects.equals(fromObjectApiName, SFAPreDefine.InvoiceApplicationLines.getApiName())) {
            configKey = INVOICE_LINES_MAPPING_RULE.getKey();
        }
        String configValue = configService.findTenantConfig(user, configKey);
        if (Objects.isNull(configValue)) {
            MultiSourceServiceModel.MultiSourceMappingRuleModel multiSourceMappingRuleModel
                    = MultiSourceServiceModel.MultiSourceMappingRuleModel.builder()
                    .objectApiName(sourceApiName)
                    .ruleApiName(ruleApiName)
                    .fieldApiName(fieldApiName)
                    .build();
            List<MultiSourceServiceModel.MultiSourceMappingRuleModel> orderPaymentMappingRuleModels = Lists.newArrayList(multiSourceMappingRuleModel);
            //新建
            configService.createTenantConfig(user, configKey, JSON.toJSONString(orderPaymentMappingRuleModels), ConfigValueType.STRING);
        } else {
            //更新
            List<MultiSourceServiceModel.MultiSourceMappingRuleModel> multiSourceMappingRuleModels
                    = JSON.parseArray(configValue, MultiSourceServiceModel.MultiSourceMappingRuleModel.class);
            Set<String> apiNamesSet = new HashSet<>();
            for (MultiSourceServiceModel.MultiSourceMappingRuleModel multiSourceMappingRuleModel : multiSourceMappingRuleModels) {
                apiNamesSet.add(multiSourceMappingRuleModel.getObjectApiName());
            }

            boolean isSourceApiNameMatched = apiNamesSet.contains(sourceApiName);

            if (!isSourceApiNameMatched) {
                MultiSourceServiceModel.MultiSourceMappingRuleModel multiSourceMappingRuleModel
                        = MultiSourceServiceModel.MultiSourceMappingRuleModel.builder()
                        .objectApiName(sourceApiName)
                        .ruleApiName(ruleApiName)
                        .fieldApiName(fieldApiName)
                        .build();
                multiSourceMappingRuleModels.add(multiSourceMappingRuleModel);
            } else {
                Optional<MultiSourceServiceModel.MultiSourceMappingRuleModel> optionalMultiSourceMappingRuleModel =
                        multiSourceMappingRuleModels.stream()
                                .filter(o -> Objects.equals(sourceApiName, o.getObjectApiName()))
                                .findFirst();
                if (optionalMultiSourceMappingRuleModel.isPresent()) {
                    MultiSourceServiceModel.MultiSourceMappingRuleModel orderPaymentMappingRuleModel = optionalMultiSourceMappingRuleModel.get();
                    orderPaymentMappingRuleModel.setRuleApiName(ruleApiName);
                }
            }

            configService.updateTenantConfig(user, configKey, JSON.toJSONString(multiSourceMappingRuleModels), ConfigValueType.STRING);
        }
    }
    private String addCountField(User user, IObjectDescribe describe, String fieldApiName,String targetApiName,String relatedFieldApiName) {
        if (StringUtils.isEmpty(relatedFieldApiName)) {
            return null;
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(relatedFieldApiName + "__c");
        if (Objects.nonNull(fieldDescribe)) {
            return relatedFieldApiName + "__c";
        }
        //订单，合同，预置related_order_payment_count
        fieldDescribe = describe.getFieldDescribe(relatedFieldApiName);
        if (Objects.nonNull(fieldDescribe)) {
            return relatedFieldApiName;
        }
        String label = Strings.EMPTY;
        if (Objects.equals(targetApiName, Utils.ORDER_PAYMENT_API_NAME)) {
            label = "关联回款";//ignoreI18n
        } else if (Objects.equals(targetApiName, SFAPreDefine.SettlementDetail.getApiName())) {
            label = "关联结算单"; //ignoreI18n
        }

        List<IFieldDescribe> addFields = Lists.newArrayList();
        addFields.add(CountFieldDescribeBuilder.builder()
                .apiName(relatedFieldApiName + "__c")
                .label(label)
                .subObjectDescribeApiName(targetApiName)
                .required(false)
                .fieldApiName(fieldApiName)
                .countType(IFieldDescribe.SUMMARY_TYPE_COUNT)
                .returnType("number")
                .countFieldApiName("")
                .decimalPlaces(0)
                .defaultResult("0")
                .defineType(IFieldDescribe.DEFINE_TYPE_CUSTOM)
                .isExtend(true)
                .countToZero(true)
                .index(true)
                .build());
        try {
            objectDescribeService.addCustomFieldDescribe(describe, addFields);
            calculateRestService.submitCalculateJob(user, Lists.newArrayList(relatedFieldApiName + "__c"), describe.getApiName());
            return relatedFieldApiName + "__c";
        } catch (MetadataServiceException e) {
            log.error("addField error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }
    private void updateFieldWheres(IObjectDescribe targetDescribe,String countFieldName, String fieldApiName,String relatedFieldApiName) {
        if (StringUtils.isEmpty(relatedFieldApiName)) {
            return;
        }
        ObjectReferenceFieldDescribe fieldDescribe = (ObjectReferenceFieldDescribe)targetDescribe.getFieldDescribe(fieldApiName);
        if (Objects.isNull(fieldDescribe)) {
            return;
        }
        List<LinkedHashMap> wheres = fieldDescribe.getWheres();
        if (CollectionUtils.isEmpty(wheres)) {
            List<LinkedHashMap> addWheres = getWheres(countFieldName);
            fieldDescribe.setWheres(addWheres);
            try {
                objectDescribeService.updateFieldDescribe(targetDescribe, Lists.newArrayList(fieldDescribe));
            } catch (MetadataServiceException e) {
                log.error("addField error",e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }
    private List<LinkedHashMap> getWheres(String relatedFieldApiName) {
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        List<Map> filters = Lists.newArrayList();
        Map<String, Object> filter = Maps.newHashMap();
        filter.put("field_name", relatedFieldApiName);
        filter.put("operator", Operator.EQ.name());
        filter.put("value_type", 0);
        filter.put("field_values", Lists.newArrayList("0"));
        filters.add(filter);
        map.put("filters", filters);
        wheres.add(map);
        return wheres;
    }
}
