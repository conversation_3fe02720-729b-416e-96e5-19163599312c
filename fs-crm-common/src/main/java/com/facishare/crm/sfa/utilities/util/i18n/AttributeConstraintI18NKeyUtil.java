package com.facishare.crm.sfa.utilities.util.i18n;

public interface AttributeConstraintI18NKeyUtil {

    /**
     * 节点唯一标识
     */
    String ATTRIBUTE_CONSTRAINT_IMPORT_NODE_ID  = "attribute.constraint.import.node_id";
    /**
     * 属性或非标属性填数字"1"，属性值或非标属性值填数字"2"
     */
    String ATTRIBUTE_CONSTRAINT_IMPORT_NODE_TYPE  = "attribute.constraint.import.node_type";

    /**
     * 填属性或非标属性的主属性
     */
    String ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_LABEL = "attribute.constrain.import.attribute.label";
    /**
     * 填属性值的主属性或非标属性值
     */
    String ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_VALUE_LABEL = "attribute.constrain.import.attribute.value.label";

    /**
     * 填属性值的主属性
     */
    String ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_DEFAULT_VALUE_LABEL = "attribute.constrain.import.attribute.default.value.label";
}
