package com.facishare.crm.sfa.predefine.service.cpq.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.BatchCalculate;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface QueryBomPriceModel {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Param{
        private String rootRowId;
        private String coreId;
        private String nodeBomCoreType;
        private String nodeBomCoreVersion;
        private String newBomPath;
        private String apiName;  //主对象名称
        private String detailApiName;  //从对象名称
        private String accountId;
        private String rootBomId ;
        private String rootSubtotal; //母件小计
        private String rootAmount; //母件数量
        private String partnerId; //合作伙伴
        private boolean priority;  //强制优先级
        private boolean noCalPrice;  //不取价计算bom价格
        private BigDecimal priceBookDiscount;//根节点价目表折扣
        private boolean switchMasterPriceBook;  //切换主对象价目表
        private String mcCurrency;
        private BatchCalculate.Arg calculateArg;
        private List<BomInfo> bomList;
        private ObjectDataDocument objectData;
        private ObjectDataDocument object_data;
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        private String virtualId;//批量处理时需要用到，非前端传过来的参数
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class BomParam{
        private String coreId;
        private String nodeBomCoreType;
        private String nodeBomCoreVersion;
        private String newBomPath;
        private String rootBomId ;
        private String rootSubtotal; //母件小计
        private String rootAmount; //母件数量
        private boolean priority;  //强制优先级
        private BigDecimal priceBookDiscount;//根节点价目表折扣
        private List<BomInfo> bomList;
    }

    /**
     * 批量计算接口参数，将公共参数提出来，减小接口请求数据量体积
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class BatchParam {
        private String apiName;  //主对象名称
        private String detailApiName;  //从对象名称
        private String accountId;
        private String partnerId; //合作伙伴
        private boolean noCalPrice;  //不取价计算bom价格
        private boolean switchMasterPriceBook;  //切换主对象价目表
        private String mcCurrency;
        private BatchCalculate.Arg calculateArg;
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        private Map<String, BomParam> newBomMap = Maps.newHashMap();
    }

    @Data
    class BomInfo{
        private String coreId;
        private String relatedCoreId;
        private String nodeBomCoreType;
        private String nodeBomCoreVersion;
        private String newBomPath;
        private String parentBomId;
        private String bomId ;
        private BigDecimal price;
        private BigDecimal pricePerSet;
        private BigDecimal originPrice;
        private String amount;
        private String productId;
        private String priceBookId;
        private String attrPriceBookId;
        private String shareRate;
        private String product_group_id;
        private String pricingPeriod; //期数
        private String pricingMode; //定价模式

        private String product_group_id__r;
        private Map attrMap;
        /**
         * 是否被修改的记录
         */
        private boolean updateFlag = false;
        private boolean advancePriceFlag = false;
        //变更的字段 quantity 、price 、pricingPeriod 、pricePerSet
        private String updateField ;
        /**
         * 节点类型
         */
        private String nodeType;
        private String rootProdKey;
        private String parentProdKey;
        private String prodKey;
        private String newPath;
        private int nodeNo;

    }

}
