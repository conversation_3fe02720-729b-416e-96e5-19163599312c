package com.facishare.crm.platform;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_OBJECT_DESCRIBE_NOT_EXISTS;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;
import static com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil.DATA_NOT_EXIST;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-23
 * ============================================================
 */
@Service
@Slf4j
public class AssertValidator {
    public void assertAllNotBlank(CharSequence... css) {
        if (StringUtils.isAllBlank(css)) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
    }

    public void assertAnyNotBlank(CharSequence... css) {
        if (StringUtils.isAllBlank(css)) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
    }

    public void assertNotNull(Object object) {
        if (object == null) {
            throw new ValidateException(I18N.text(DATA_NOT_EXIST));
        }
    }

    public void assertAllNotNull(Object... objects) {
        for (Object obj : objects) {
            if (obj == null) {
                throw new ValidateException(I18N.text(DATA_NOT_EXIST));
            }
        }
    }

    public void assertNotNullDescribe(Object describe, String objectDescribeApiName) {
        if (describe == null) {
            throw new ValidateException(I18N.text(SFA_OBJECT_DESCRIBE_NOT_EXISTS, objectDescribeApiName));
        }
    }
}
