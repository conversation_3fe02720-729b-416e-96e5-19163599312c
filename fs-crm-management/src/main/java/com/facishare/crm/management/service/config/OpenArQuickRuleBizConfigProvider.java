package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.ArrayFieldDescribeBuilder;
import com.facishare.crm.management.service.AttributeGroupInitService;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.DataOrganizationUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypePojo;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * 配置 是否开启属性属性值
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenArQuickRuleBizConfigProvider extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_AR_QUICK_RULE.getId();
    }


    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        /*List<String> describeList = Lists.newArrayList(
                SFAPreDefineObject.AccountsReceivableQuickRule.getApiName()
                ,SFAPreDefineObject.ObjectArRuleRelated.getApiName()
        );
        Map<String, IObjectDescribe> describiMap = getDescribeWithSimplifiedChineseByApiNames(user, describeList);
        for (String describeName : describeList) {
            //刷描述
            IObjectDescribe describe = describiMap.get(describeName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), describeName);
            }
        }*/

        //功能权限和按钮
        //serviceFacade.batchCreateFunc(user, Utils.SALES_ORDER_API_NAME, Lists.newArrayList(ObjectAction.Q.getActionCode()));
        //serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.AssociateAttribute.getActionCode()), Lists.newArrayList());

        super.setConfigValue(user, value, oldValue, key);
    }
}
